#!/usr/bin/env python3
"""
修复现有任务的Core服务配置
为缺少core_service_id的任务添加合适的Core服务配置
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
import logging
from app.core.schemas.social_repository import SocialDatabaseService
from app.config.database import DatabaseConfig
import motor.motor_asyncio

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_task_core_service(task_id: str, core_service_id: str = None):
    """修复指定任务的Core服务配置"""
    try:
        # 初始化数据库连接
        db_config = DatabaseConfig()
        client = motor.motor_asyncio.AsyncIOMotorClient(db_config.mongodb_url)
        db = client[db_config.database_name]
        db_service = SocialDatabaseService(db)
        
        # 查找任务
        task = await db_service.db.social_tasks.find_one({'task_id': task_id})
        
        if not task:
            logger.error(f"❌ 未找到任务: {task_id}")
            return False
            
        logger.info(f"=== 任务信息 ===")
        logger.info(f"任务ID: {task.get('task_id')}")
        logger.info(f"任务类型: {task.get('task_type')}")
        logger.info(f"状态: {task.get('status')}")
        logger.info(f"当前Core服务ID: {task.get('core_service_id', '❌ 未设置')}")
        logger.info(f"父任务ID: {task.get('parent_task_id', '无')}")
        
        # 确定要设置的Core服务ID
        target_core_service_id = core_service_id
        
        # 如果没有指定Core服务ID，尝试从父任务或设备映射中推断
        if not target_core_service_id:
            # 如果是子任务，尝试从父任务获取
            if task.get('parent_task_id'):
                parent_task = await db_service.db.social_tasks.find_one({'task_id': task['parent_task_id']})
                if parent_task and parent_task.get('core_service_id'):
                    target_core_service_id = parent_task['core_service_id']
                    logger.info(f"从父任务继承Core服务ID: {target_core_service_id}")
            
            # 如果仍然没有，尝试从设备映射中获取
            if not target_core_service_id and task.get('account_id') and task.get('platform_id'):
                mapping = await db_service.db.device_account_mappings.find_one({
                    'account_id': task['account_id'],
                    'platform_id': task['platform_id'],
                    'status': 'active'
                })
                if mapping and mapping.get('core_service_id'):
                    target_core_service_id = mapping['core_service_id']
                    logger.info(f"从设备映射获取Core服务ID: {target_core_service_id}")
        
        # 如果仍然没有，使用默认值
        if not target_core_service_id:
            # 这里可以设置一个默认的Core服务ID，或者询问用户
            logger.warning("无法自动确定Core服务ID，请手动指定")
            return False
            
        # 更新任务的Core服务配置
        update_result = await db_service.db.social_tasks.update_one(
            {'task_id': task_id},
            {'$set': {'core_service_id': target_core_service_id}}
        )
        
        if update_result.modified_count > 0:
            logger.info(f"✅ 成功为任务 {task_id} 设置Core服务ID: {target_core_service_id}")
            
            # 如果是主任务，也更新所有子任务
            if task.get('task_type') == 'main':
                subtasks_result = await db_service.db.social_tasks.update_many(
                    {'parent_task_id': task_id},
                    {'$set': {'core_service_id': target_core_service_id}}
                )
                if subtasks_result.modified_count > 0:
                    logger.info(f"✅ 同时更新了 {subtasks_result.modified_count} 个子任务的Core服务ID")
            
            return True
        else:
            logger.warning(f"⚠️ 任务 {task_id} 未被更新（可能已经有相同的Core服务ID）")
            return False
            
    except Exception as e:
        logger.error(f"❌ 修复任务Core服务配置失败: {str(e)}", exc_info=True)
        return False
    finally:
        if 'client' in locals():
            client.close()

async def check_and_fix_task(task_id: str):
    """检查并修复指定任务"""
    try:
        # 初始化数据库连接
        db_config = DatabaseConfig()
        client = motor.motor_asyncio.AsyncIOMotorClient(db_config.mongodb_url)
        db = client[db_config.database_name]
        
        # 查找任务
        task = await db.social_tasks.find_one({'task_id': task_id})
        
        if not task:
            logger.error(f"❌ 未找到任务: {task_id}")
            return
            
        logger.info(f"=== 检查任务 {task_id} ===")
        logger.info(f"任务类型: {task.get('task_type')}")
        logger.info(f"状态: {task.get('status')}")
        logger.info(f"Core服务ID: {task.get('core_service_id', '❌ 未设置')}")
        logger.info(f"父任务ID: {task.get('parent_task_id', '无')}")
        
        # 检查是否需要修复
        if not task.get('core_service_id'):
            logger.warning("❌ 任务缺少core_service_id，需要修复")
            
            # 尝试推断合适的Core服务ID
            suggested_core_id = None
            
            # 如果是子任务，检查父任务
            if task.get('parent_task_id'):
                parent_task = await db.social_tasks.find_one({'task_id': task['parent_task_id']})
                if parent_task and parent_task.get('core_service_id'):
                    suggested_core_id = parent_task['core_service_id']
                    logger.info(f"建议从父任务继承Core服务ID: {suggested_core_id}")
            
            # 检查设备映射
            if not suggested_core_id and task.get('account_id') and task.get('platform_id'):
                mapping = await db.device_account_mappings.find_one({
                    'account_id': task['account_id'],
                    'platform_id': task['platform_id'],
                    'status': 'active'
                })
                if mapping and mapping.get('core_service_id'):
                    suggested_core_id = mapping['core_service_id']
                    logger.info(f"建议从设备映射获取Core服务ID: {suggested_core_id}")
            
            if suggested_core_id:
                logger.info(f"建议为任务设置Core服务ID: {suggested_core_id}")
                logger.info("可以运行以下命令进行修复:")
                logger.info(f"python fix_existing_task_core_service.py --fix {task_id} --core-service {suggested_core_id}")
            else:
                logger.warning("无法自动推断合适的Core服务ID，需要手动指定")
        else:
            logger.info(f"✅ 任务已有Core服务ID: {task.get('core_service_id')}")
            
    except Exception as e:
        logger.error(f"❌ 检查任务失败: {str(e)}", exc_info=True)
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='修复现有任务的Core服务配置')
    parser.add_argument('task_id', help='任务ID')
    parser.add_argument('--fix', action='store_true', help='执行修复操作')
    parser.add_argument('--core-service', help='指定Core服务ID')
    
    args = parser.parse_args()
    
    if args.fix:
        if not args.core_service:
            logger.error("执行修复时必须指定 --core-service 参数")
            sys.exit(1)
        logger.info(f"开始修复任务 {args.task_id}，设置Core服务ID为: {args.core_service}")
        success = asyncio.run(fix_task_core_service(args.task_id, args.core_service))
        if success:
            logger.info("✅ 修复完成")
        else:
            logger.error("❌ 修复失败")
            sys.exit(1)
    else:
        logger.info(f"检查任务 {args.task_id} 的Core服务配置")
        asyncio.run(check_and_fix_task(args.task_id))
