#!/usr/bin/env python3
"""
检查指定任务的Core服务使用情况
"""

import pymongo
import json
from datetime import datetime

def check_task(task_id):
    """检查任务的Core服务配置"""
    try:
        # 连接MongoDB
        client = pymongo.MongoClient('mongodb://192.168.123.137:27017/')
        db = client['thunderhub']
        
        # 查找任务
        task = db.social_tasks.find_one({'task_id': task_id})
        
        if task:
            print('=== 任务详细信息 ===')
            print(f'任务ID: {task.get("task_id")}')
            print(f'任务类型: {task.get("task_type")}')
            print(f'平台ID: {task.get("platform_id")}')
            print(f'账号ID: {task.get("account_id")}')
            print(f'设备ID: {task.get("device_id")}')
            print(f'状态: {task.get("status")}')
            print(f'Core服务ID: {task.get("core_service_id", "❌ 未设置")}')
            print(f'父任务ID: {task.get("parent_task_id", "无")}')
            print(f'创建时间: {task.get("created_at")}')
            print(f'开始时间: {task.get("start_time", "未开始")}')
            
            # 检查是否有metadata
            if 'metadata' in task:
                print(f'Metadata: 存在 ({len(task["metadata"])} 个字段)')
            else:
                print('Metadata: 不存在')
            
            # 如果是子任务，查看父任务信息
            if task.get('parent_task_id'):
                parent_task = db.social_tasks.find_one({'task_id': task['parent_task_id']})
                if parent_task:
                    print(f'\n=== 父任务信息 ===')
                    print(f'父任务Core服务ID: {parent_task.get("core_service_id", "❌ 未设置")}')
                    print(f'父任务状态: {parent_task.get("status")}')
                    print(f'父任务类型: {parent_task.get("task_type")}')
                    
                    # 检查所有子任务的Core服务配置
                    subtasks = list(db.social_tasks.find({'parent_task_id': task['parent_task_id']}))
                    print(f'\n=== 所有子任务Core服务配置 ===')
                    for i, subtask in enumerate(subtasks, 1):
                        core_id = subtask.get("core_service_id", "❌ 未设置")
                        status = subtask.get("status", "未知")
                        print(f'子任务{i} ({subtask["task_id"][:8]}...): Core服务={core_id}, 状态={status}')
            
            # 分析问题
            print(f'\n=== 问题分析 ===')
            core_service_id = task.get("core_service_id")
            if not core_service_id:
                print('❌ 问题确认：任务数据中缺少core_service_id字段')
                print('   这意味着任务创建时没有正确保存Core服务信息')
                print('   任务启动时会使用默认的服务发现机制')
                
                # 检查父任务是否有Core服务配置
                if task.get('parent_task_id'):
                    parent_task = db.social_tasks.find_one({'task_id': task['parent_task_id']})
                    if parent_task and parent_task.get("core_service_id"):
                        print(f'   父任务有Core服务配置: {parent_task["core_service_id"]}')
                        print('   建议：子任务应该继承父任务的Core服务配置')
                    else:
                        print('   父任务也没有Core服务配置')
            else:
                print(f'✅ 任务有Core服务配置: {core_service_id}')
                print('   如果任务仍然使用了错误的Core服务，可能是任务启动逻辑的问题')
                
        else:
            print('❌ 未找到指定的任务')
            
        client.close()
        
    except Exception as e:
        print(f'❌ 检查任务失败: {str(e)}')

if __name__ == "__main__":
    task_id = "93badfee-12f3-4093-bd74-29ccf672986b"
    check_task(task_id)
