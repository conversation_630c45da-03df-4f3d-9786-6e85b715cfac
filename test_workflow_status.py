#!/usr/bin/env python3
"""
测试工作流状态获取功能
验证Backend是否能正确获取Core服务的真实工作流执行状态
"""

import asyncio
import aiohttp
import json
import redis.asyncio as redis
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_workflow_status():
    """测试工作流状态获取"""
    
    # 1. 首先创建一个模拟的工作流状态到Redis
    logger.info("1. 创建模拟工作流状态到Redis")
    
    test_task_id = "test_task_12345"
    mock_workflow_state = {
        "workflow_id": f"workflow_{test_task_id}",
        "workflow_name": "YouTube Shorts上传工作流",
        "workflow_version": "1.0",
        "current_step_index": 2,
        "total_steps": 5,
        "can_resume": True,
        "context_data": {"device_id": "test_device"},
        "checkpoint_data": {"last_checkpoint": "step_2_completed"},
        "steps": [
            {
                "id": "step_1",
                "name": "连接设备",
                "description": "连接到Android设备",
                "status": "completed",
                "progress": 100,
                "start_time": "2024-01-01T10:00:00",
                "end_time": "2024-01-01T10:00:30",
                "duration": 30,
                "required": True,
                "logs": ["设备连接成功"]
            },
            {
                "id": "step_2", 
                "name": "打开YouTube应用",
                "description": "启动YouTube应用",
                "status": "completed",
                "progress": 100,
                "start_time": "2024-01-01T10:00:30",
                "end_time": "2024-01-01T10:01:00",
                "duration": 30,
                "required": True,
                "logs": ["应用启动成功"]
            },
            {
                "id": "step_3",
                "name": "上传视频",
                "description": "选择并上传视频文件",
                "status": "running",
                "progress": 45,
                "start_time": "2024-01-01T10:01:00",
                "required": True,
                "logs": ["开始上传视频", "上传进度: 45%"]
            },
            {
                "id": "step_4",
                "name": "设置视频信息",
                "description": "设置标题、描述等信息",
                "status": "pending",
                "progress": 0,
                "required": True,
                "logs": []
            },
            {
                "id": "step_5",
                "name": "发布视频",
                "description": "确认发布视频",
                "status": "pending", 
                "progress": 0,
                "required": True,
                "logs": []
            }
        ]
    }
    
    try:
        # 连接Redis并保存模拟状态
        redis_client = redis.Redis.from_url("redis://***************:6379/1")
        redis_key = f"workflow_state:{test_task_id}"
        workflow_json = json.dumps(mock_workflow_state, ensure_ascii=False, default=str)
        
        await redis_client.set(redis_key, workflow_json)
        await redis_client.expire(redis_key, 3600)  # 1小时过期
        
        logger.info(f"✅ 模拟工作流状态已保存到Redis: {redis_key}")
        
        # 验证保存是否成功
        saved_data = await redis_client.get(redis_key)
        if saved_data:
            logger.info(f"✅ Redis中的数据验证成功")
        else:
            logger.error(f"❌ Redis中没有找到数据")
            return
            
        await redis_client.close()
        
    except Exception as e:
        logger.error(f"❌ Redis操作失败: {str(e)}")
        return
    
    # 2. 测试Backend API获取工作流状态
    logger.info("2. 测试Backend API获取工作流状态")
    
    try:
        async with aiohttp.ClientSession() as session:
            url = f"http://localhost:8000/api/v1/workflow/{test_task_id}/workflow"
            logger.info(f"请求URL: {url}")
            
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ API调用成功")
                    logger.info(f"✅ 数据源: {data.get('source', 'unknown')}")
                    
                    if data.get('success') and data.get('data'):
                        workflow_data = data['data']
                        logger.info(f"✅ 工作流名称: {workflow_data.get('workflow_name')}")
                        logger.info(f"✅ 当前步骤索引: {workflow_data.get('current_step_index')}")
                        logger.info(f"✅ 总步骤数: {workflow_data.get('total_steps')}")
                        
                        steps = workflow_data.get('steps', [])
                        logger.info(f"✅ 步骤详情:")
                        for i, step in enumerate(steps):
                            status = step.get('status', 'unknown')
                            progress = step.get('progress', 0)
                            name = step.get('name', 'Unknown')
                            logger.info(f"   步骤 {i+1}: {name} - {status} ({progress}%)")
                        
                        # 验证是否获取到了真实状态
                        if workflow_data.get('current_step_index') == 2:
                            logger.info(f"✅ 成功获取到真实的工作流执行状态！")
                        else:
                            logger.warning(f"⚠️ 获取到的可能不是真实状态")
                            
                    else:
                        logger.error(f"❌ API返回数据格式错误: {data}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API调用失败: {response.status} - {error_text}")
                    
    except Exception as e:
        logger.error(f"❌ API测试失败: {str(e)}")
    
    # 3. 清理测试数据
    logger.info("3. 清理测试数据")
    try:
        redis_client = redis.Redis.from_url("redis://***************:6379/1")
        await redis_client.delete(redis_key)
        await redis_client.close()
        logger.info(f"✅ 测试数据已清理")
    except Exception as e:
        logger.warning(f"⚠️ 清理测试数据失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_workflow_status())
