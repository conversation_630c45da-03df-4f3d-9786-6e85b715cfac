#!/usr/bin/env python3
"""
测试gRPC连接
验证Backend是否能正确连接到Core服务并获取工作流配置
"""

import asyncio
import sys
import os

# 添加backend路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_grpc_connection():
    """测试gRPC连接"""
    try:
        print("1. 测试gRPC客户端连接...")
        
        from backend.app.core.grpc_client import get_task_client
        
        # 获取任务客户端
        print("正在获取Core服务任务客户端...")
        task_client = await get_task_client()
        
        if not task_client:
            print("❌ 无法获取Core服务任务客户端")
            print("可能的原因：")
            print("  1. Core服务未启动")
            print("  2. Consul服务未启动或Core服务未注册到Consul")
            print("  3. gRPC端口不可访问")
            return False
            
        print("✅ 成功获取Core服务任务客户端")
        
        # 测试获取工作流配置
        print("2. 测试获取工作流配置...")
        
        platform_id = "youtube"
        content_type = "shorts"
        
        print(f"请求参数: platform_id={platform_id}, content_type={content_type}")
        
        workflow_config = await task_client.get_workflow_config(platform_id, content_type)
        
        if workflow_config and workflow_config.get("success"):
            print("✅ 成功获取工作流配置")
            print(f"工作流名称: {workflow_config.get('workflow_name')}")
            print(f"工作流版本: {workflow_config.get('workflow_version')}")
            print(f"步骤数量: {len(workflow_config.get('steps', []))}")
            
            # 显示前几个步骤
            steps = workflow_config.get('steps', [])
            print("前5个步骤:")
            for i, step in enumerate(steps[:5]):
                name = step.get('name', 'Unknown')
                action = step.get('action', 'unknown')
                print(f"  步骤 {i+1}: {name} (Action: {action})")
                
            return True
        else:
            print("❌ 获取工作流配置失败")
            error = workflow_config.get('error', '未知错误') if workflow_config else '无响应'
            print(f"错误信息: {error}")
            return False
            
    except Exception as e:
        print(f"❌ gRPC连接测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_consul_discovery():
    """测试Consul服务发现"""
    try:
        print("3. 测试Consul服务发现...")
        
        from backend.app.services.consul import ConsulDiscovery
        
        consul_discovery = ConsulDiscovery()
        services = consul_discovery.get_all_services("thunderhub-core")
        
        if services:
            print(f"✅ 从Consul发现 {len(services)} 个Core服务:")
            for service_id, service_info in services.items():
                host = service_info.get('host', 'unknown')
                port = service_info.get('port', 'unknown')
                print(f"  服务ID: {service_id}, 地址: {host}:{port}")
            return True
        else:
            print("❌ 未从Consul发现任何Core服务")
            print("可能的原因：")
            print("  1. Consul服务未启动")
            print("  2. Core服务未注册到Consul")
            print("  3. 服务名称不匹配")
            return False
            
    except Exception as e:
        print(f"❌ Consul服务发现测试失败: {str(e)}")
        return False

async def test_direct_grpc_connection():
    """测试直接gRPC连接（不通过Consul）"""
    try:
        print("4. 测试直接gRPC连接...")
        
        from backend.app.core.client import TaskServiceClient
        
        # 直接连接到localhost:50051
        client = TaskServiceClient(host='localhost', port=50051)
        
        workflow_config = await client.get_workflow_config("youtube", "shorts")
        
        if workflow_config and workflow_config.get("success"):
            print("✅ 直接gRPC连接成功")
            print(f"工作流名称: {workflow_config.get('workflow_name')}")
            return True
        else:
            print("❌ 直接gRPC连接失败")
            error = workflow_config.get('error', '未知错误') if workflow_config else '无响应'
            print(f"错误信息: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 直接gRPC连接测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("开始测试gRPC连接和工作流配置获取")
    print("=" * 50)
    
    # 测试Consul服务发现
    consul_ok = await test_consul_discovery()
    print()
    
    # 测试gRPC连接
    grpc_ok = await test_grpc_connection()
    print()
    
    # 如果通过Consul连接失败，尝试直接连接
    if not grpc_ok:
        direct_ok = await test_direct_grpc_connection()
        print()
    else:
        direct_ok = True
    
    # 总结
    print("=" * 50)
    print("测试结果总结:")
    print(f"Consul服务发现: {'✅ 成功' if consul_ok else '❌ 失败'}")
    print(f"gRPC连接: {'✅ 成功' if grpc_ok else '❌ 失败'}")
    print(f"直接连接: {'✅ 成功' if direct_ok else '❌ 失败'}")
    
    if grpc_ok or direct_ok:
        print("🎉 gRPC连接正常，Backend可以从Core服务获取工作流配置")
    else:
        print("❌ gRPC连接失败，需要检查Core服务状态")
        print("\n解决方案：")
        print("1. 确保Core服务正在运行")
        print("2. 确保Consul服务正在运行")
        print("3. 检查防火墙和端口配置")
        print("4. 检查Core服务的gRPC端口（默认50051）")

if __name__ == "__main__":
    asyncio.run(main())
