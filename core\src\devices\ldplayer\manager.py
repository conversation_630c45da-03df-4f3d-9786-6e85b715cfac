"""
雷电模拟器管理器
管理多个雷电模拟器实例
"""

import os
import asyncio
import logging
import json
from typing import Dict, List, Any, Optional

from src.devices.ldplayer.controller import LDPlayerController
from src.devices.ldplayer.types import LDPlayerConfig
from src.devices.base import DeviceStatus, DeviceInfo

logger = logging.getLogger(__name__)

class LDPlayerManager:
    """雷电模拟器管理器"""

    def __init__(self, ldconsole_path: str):
        """初始化雷电模拟器管理器

        Args:
            ldconsole_path: ldconsole.exe路径
        """
        self.ldconsole_path = ldconsole_path
        if not os.path.exists(ldconsole_path):
            logger.error(f"ldconsole.exe不存在: {ldconsole_path}")
            raise FileNotFoundError(f"ldconsole.exe不存在: {ldconsole_path}")

        self.devices: Dict[str, LDPlayerController] = {}
        logger.info(f"雷电模拟器管理器初始化，ldconsole路径: {ldconsole_path}")

    async def initialize(self) -> bool:
        """初始化管理器"""
        try:
            # 获取所有模拟器列表
            device_list = await self._get_device_list()

            # 初始化所有设备控制器
            for device_info in device_list:
                device_id = device_info.get('index')
                if not device_id:
                    continue

                config = {
                    'device_id': device_id,
                    'name': device_info.get('name', f'模拟器-{device_id}'),  # 使用list2命令获取的真实设备名称
                    'device_type': '雷电模拟器',    # 明确设置设备类型
                    'ldconsole_path': self.ldconsole_path
                }

                controller = LDPlayerController(config)
                self.devices[device_id] = controller
                logger.info(f"已添加设备: {device_id}")

            logger.info(f"雷电模拟器管理器初始化完成，共{len(self.devices)}个设备")
            return True

        except Exception as e:
            logger.error(f"初始化雷电模拟器管理器异常: {str(e)}", exc_info=True)
            return False

    async def get_device(self, device_id: str) -> Optional[LDPlayerController]:
        """获取设备控制器

        Args:
            device_id: 设备ID

        Returns:
            设备控制器，如果不存在则返回None
        """
        return self.devices.get(device_id)

    async def get_all_devices(self) -> List[DeviceInfo]:
        """获取所有设备信息

        Returns:
            设备信息列表
        """
        result = []
        for device_id, controller in self.devices.items():
            try:
                device_info = await controller.get_info()
                result.append(device_info)
            except Exception as e:
                logger.error(f"获取设备{device_id}信息异常: {str(e)}", exc_info=True)

        return result

    async def create_device(self, config: LDPlayerConfig) -> Optional[str]:
        """创建新设备

        Args:
            config: 设备配置

        Returns:
            新设备ID，如果创建失败则返回None
        """
        try:
            # 构建创建命令
            cmd_parts = [f'"{self.ldconsole_path}"', 'add']

            # 添加名称
            name = config.get('name', f'模拟器-{len(self.devices) + 1}')
            cmd_parts.append(f'--name {name}')

            # 执行命令
            cmd = ' '.join(cmd_parts)
            logger.info(f"执行命令: {cmd}")

            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"创建设备失败: {stderr.decode()}")
                return None

            # 解析输出，获取新设备ID
            output = stdout.decode().strip()
            try:
                device_id = output
                logger.info(f"创建设备成功，ID: {device_id}")

                # 创建设备控制器
                controller_config = {
                    'device_id': device_id,
                    'name': name,
                    'ldconsole_path': self.ldconsole_path
                }

                controller = LDPlayerController(controller_config)
                self.devices[device_id] = controller

                # 配置设备
                await self._configure_device(device_id, config)

                return device_id
            except Exception as e:
                logger.error(f"解析新设备ID异常: {str(e)}", exc_info=True)
                return None

        except Exception as e:
            logger.error(f"创建设备异常: {str(e)}", exc_info=True)
            return None

    async def remove_device(self, device_id: str) -> bool:
        """删除设备

        Args:
            device_id: 设备ID

        Returns:
            是否删除成功
        """
        try:
            # 先停止设备
            controller = self.devices.get(device_id)
            if controller:
                await controller.stop()

            # 构建删除命令
            cmd = f'"{self.ldconsole_path}" remove --index {device_id}'
            logger.info(f"执行命令: {cmd}")

            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"删除设备{device_id}失败: {stderr.decode()}")
                return False

            # 从设备列表中移除
            if device_id in self.devices:
                del self.devices[device_id]

            logger.info(f"删除设备{device_id}成功")
            return True

        except Exception as e:
            logger.error(f"删除设备{device_id}异常: {str(e)}", exc_info=True)
            return False

    async def start_device(self, device_id: str) -> bool:
        """启动单个设备

        Args:
            device_id: 设备ID

        Returns:
            是否启动成功
        """
        try:
            controller = self.devices.get(device_id)
            if not controller:
                logger.error(f"设备{device_id}不存在")
                return False

            success = await controller.start()
            logger.info(f"设备{device_id}启动{'成功' if success else '失败'}")
            return success

        except Exception as e:
            logger.error(f"启动设备{device_id}异常: {str(e)}", exc_info=True)
            return False

    async def stop_device(self, device_id: str) -> bool:
        """停止单个设备

        Args:
            device_id: 设备ID

        Returns:
            是否停止成功
        """
        try:
            controller = self.devices.get(device_id)
            if not controller:
                logger.error(f"设备{device_id}不存在")
                return False

            success = await controller.stop()
            logger.info(f"设备{device_id}停止{'成功' if success else '失败'}")
            return success

        except Exception as e:
            logger.error(f"停止设备{device_id}异常: {str(e)}", exc_info=True)
            return False

    async def start_all_devices(self) -> Dict[str, bool]:
        """启动所有设备

        Returns:
            设备ID与启动结果的映射
        """
        result = {}
        for device_id, controller in self.devices.items():
            try:
                success = await controller.start()
                result[device_id] = success
            except Exception as e:
                logger.error(f"启动设备{device_id}异常: {str(e)}", exc_info=True)
                result[device_id] = False

        return result

    async def stop_all_devices(self) -> Dict[str, bool]:
        """停止所有设备

        Returns:
            设备ID与停止结果的映射
        """
        result = {}
        for device_id, controller in self.devices.items():
            try:
                success = await controller.stop()
                result[device_id] = success
            except Exception as e:
                logger.error(f"停止设备{device_id}异常: {str(e)}", exc_info=True)
                result[device_id] = False

        return result

    async def shutdown(self, stop_devices: bool = False) -> None:
        """关闭管理器

        Args:
            stop_devices: 是否停止模拟器设备，默认为False（保持运行）
        """
        logger.info("正在关闭雷电模拟器管理器...")

        if stop_devices:
            # 停止所有设备
            logger.info("正在停止所有模拟器设备...")
            try:
                await self.stop_all_devices()
                logger.info("所有设备已停止")
            except Exception as e:
                logger.error(f"停止所有设备异常: {str(e)}", exc_info=True)
        else:
            # 🔧 重要修改：Core服务关闭时不关闭模拟器
            # 模拟器可能被其他服务使用，或需要保持运行状态以便快速恢复
            logger.info("Core服务关闭时保持模拟器运行状态")

        # 清理内存中的设备引用
        device_count = len(self.devices)
        self.devices.clear()

        if stop_devices:
            logger.info(f"雷电模拟器管理器已关闭，已停止 {device_count} 个模拟器")
        else:
            logger.info(f"雷电模拟器管理器已关闭，保持 {device_count} 个模拟器运行")

    async def _get_device_list(self) -> List[Dict[str, Any]]:
        """获取所有模拟器列表"""
        cmd = f'"{self.ldconsole_path}" list2'
        logger.info(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"获取设备列表失败: {stderr.decode('gbk', errors='ignore')}")
                return []

            # 解析输出 - 使用GBK编码而不是UTF-8
            try:
                # 首先尝试使用GBK解码
                output = stdout.decode('gbk', errors='ignore').strip()

                # 尝试解析为JSON
                try:
                    device_list = json.loads(output)
                    logger.info(f"成功解析JSON格式设备列表，共{len(device_list)}个设备")
                    return device_list
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试解析为CSV格式
                    logger.info("JSON解析失败，尝试解析为CSV格式")

                    device_list = []
                    lines = output.split('\n')
                    for line in lines:
                        if not line.strip():
                            continue

                        parts = line.split(',')
                        if len(parts) >= 2:
                            device_id = parts[0].strip()
                            name = parts[1].strip()

                            device_info = {
                                'index': device_id,
                                'name': name
                            }

                            device_list.append(device_info)

                    logger.info(f"成功解析CSV格式设备列表，共{len(device_list)}个设备")
                    return device_list
            except Exception as e:
                # 如果解析失败，记录详细信息
                logger.error(f"解析设备列表失败: {e}")
                logger.debug(f"原始输出: {output}")
                return []

        except Exception as e:
            logger.error(f"获取设备列表异常: {str(e)}", exc_info=True)
            return []

    async def _configure_device(self, device_id: str, config: LDPlayerConfig) -> bool:
        """配置设备

        Args:
            device_id: 设备ID
            config: 设备配置

        Returns:
            是否配置成功
        """
        try:
            # 设置分辨率
            if 'resolution' in config:
                await self._set_property(device_id, 'resolution', config['resolution'])

            # 设置CPU核心数
            if 'cpu_count' in config:
                await self._set_property(device_id, 'cpu_count', str(config['cpu_count']))

            # 设置内存大小
            if 'memory_size' in config:
                await self._set_property(device_id, 'memory_size', str(config['memory_size']))

            # 设置制造商
            if 'manufacturer' in config:
                await self._set_property(device_id, 'manufacturer', config['manufacturer'])

            # 设置型号
            if 'model' in config:
                await self._set_property(device_id, 'model', config['model'])

            # 设置手机号码
            if 'pnumber' in config:
                await self._set_property(device_id, 'pnumber', config['pnumber'])

            # 设置IMEI
            if 'imei' in config:
                await self._set_property(device_id, 'imei', config['imei'])

            # 设置MAC地址
            if 'mac_address' in config:
                await self._set_property(device_id, 'mac_address', config['mac_address'])

            # 设置Android ID
            if 'android_id' in config:
                await self._set_property(device_id, 'android_id', config['android_id'])

            logger.info(f"设备{device_id}配置完成")
            return True

        except Exception as e:
            logger.error(f"配置设备{device_id}异常: {str(e)}", exc_info=True)
            return False

    async def _set_property(self, device_id: str, property_name: str, property_value: str) -> bool:
        """设置设备属性

        Args:
            device_id: 设备ID
            property_name: 属性名
            property_value: 属性值

        Returns:
            是否设置成功
        """
        cmd = f'"{self.ldconsole_path}" modify --index {device_id} --{property_name} {property_value}'
        logger.debug(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"设置设备{device_id}属性{property_name}失败: {stderr.decode()}")
                return False

            return True

        except Exception as e:
            logger.error(f"设置设备{device_id}属性{property_name}异常: {str(e)}", exc_info=True)
            return False






