#!/usr/bin/env python3
"""
检查任务数据
查看任务的实际contentType和工作流名称
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient

async def check_task_data():
    """检查任务数据"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient("mongodb://192.168.123.137:27017")
        db = client["social_media_automation"]
        
        # 查找任务
        task_id = "5d7c659e-947c-4066-a3a2-7f31088daf33"
        task = await db.social_tasks.find_one({"task_id": task_id})
        
        if task:
            print(f"任务ID: {task.get('task_id')}")
            print(f"平台ID: {task.get('platform_id')}")
            print(f"工作流名称: {task.get('workflow_name')}")
            print(f"状态: {task.get('status')}")
            
            metadata = task.get('metadata', {})
            print(f"元数据: {metadata}")
            print(f"contentType: {metadata.get('contentType', '未设置')}")
            print(f"content_type: {metadata.get('content_type', '未设置')}")
            
            # 根据工作流名称推断内容类型
            workflow_name = task.get("workflow_name", "").lower()
            print(f"工作流名称(小写): {workflow_name}")
            
            if "shorts" in workflow_name or "短视频" in workflow_name:
                inferred_type = "shorts"
            elif "video" in workflow_name or "视频" in workflow_name:
                inferred_type = "video"
            else:
                inferred_type = "video"  # 默认值
                
            print(f"推断的内容类型: {inferred_type}")
            
            # 检查平台信息
            platform_object_id = task.get("platform_id", "")
            platform = await db.social_platforms.find_one({"_id": platform_object_id})
            if platform:
                print(f"平台信息: {platform.get('name')} (ID: {platform.get('id')})")
            else:
                print(f"未找到平台信息: {platform_object_id}")
                
        else:
            print(f"未找到任务: {task_id}")
            
        await client.close()
        
    except Exception as e:
        print(f"检查任务数据失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_task_data())
