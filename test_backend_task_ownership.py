#!/usr/bin/env python3
"""
测试Backend任务归属机制
验证多Backend实例环境下的任务消息过滤
"""

import asyncio
import logging
import json
import time
from unittest.mock import Mock, AsyncMock

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockRedisClient:
    """模拟Redis客户端"""
    
    def __init__(self):
        self.data = {}
        self.published_messages = []
    
    async def get(self, key):
        """获取键值"""
        value = self.data.get(key)
        return value.encode('utf-8') if value else None
    
    async def setex(self, key, seconds, value):
        """设置键值和过期时间"""
        self.data[key] = value
        return True
    
    async def publish(self, channel, message):
        """发布消息"""
        self.published_messages.append({
            'channel': channel,
            'message': message,
            'timestamp': time.time()
        })
        return 1

class MockMongoDB:
    """模拟MongoDB"""
    
    def __init__(self):
        self.social_tasks = MockCollection()

class MockCollection:
    """模拟MongoDB集合"""
    
    def __init__(self):
        self.tasks = {}
    
    def find_one(self, query):
        """查找单个文档"""
        task_id = query.get('task_id')
        return self.tasks.get(task_id)
    
    def insert_one(self, document):
        """插入文档"""
        task_id = document.get('task_id')
        if task_id:
            self.tasks[task_id] = document
        return Mock(inserted_id='mock_id')

class MockRedisSyncService:
    """模拟Redis同步服务"""
    
    def __init__(self, backend_instance_id):
        self.backend_instance_id = backend_instance_id
        self.redis_client = MockRedisClient()
        self.mongo_db = MockMongoDB()
        self.processed_tasks = []
        self.skipped_tasks = []
    
    async def _get_task_owner(self, task_id: str) -> str:
        """获取任务归属"""
        # 首先从Redis缓存中查找
        cache_key = f"task:{task_id}:owner"
        cached_owner = await self.redis_client.get(cache_key)
        if cached_owner:
            return cached_owner.decode('utf-8') if isinstance(cached_owner, bytes) else cached_owner
        
        # 从MongoDB查找
        task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
        if task and "backend_instance_id" in task:
            owner = task["backend_instance_id"]
            await self.redis_client.setex(cache_key, 3600, owner)
            return owner
        
        return None
    
    async def handle_task_message(self, task_id: str, task_data: dict):
        """处理任务消息"""
        # 检查任务归属
        task_owner = await self._get_task_owner(task_id)
        if task_owner and task_owner != self.backend_instance_id:
            logger.info(f"⏭️ Backend({self.backend_instance_id}) 跳过任务{task_id}，归属于: {task_owner}")
            self.skipped_tasks.append(task_id)
            return False
        
        # 处理任务
        logger.info(f"🔔 Backend({self.backend_instance_id}) 处理任务{task_id}状态更新")
        self.processed_tasks.append(task_id)
        return True

async def test_task_ownership_filtering():
    """测试任务归属过滤机制"""
    logger.info("=== 测试任务归属过滤机制 ===")
    
    # 创建3个Backend实例
    backend1 = MockRedisSyncService("backend-server1")
    backend2 = MockRedisSyncService("backend-server2") 
    backend3 = MockRedisSyncService("backend-server3")
    
    # 创建测试任务，分别归属于不同Backend
    tasks = [
        {"task_id": "task-001", "backend_instance_id": "backend-server1", "status": "running"},
        {"task_id": "task-002", "backend_instance_id": "backend-server2", "status": "running"},
        {"task_id": "task-003", "backend_instance_id": "backend-server1", "status": "completed"},
        {"task_id": "task-004", "backend_instance_id": "backend-server3", "status": "failed"},
        {"task_id": "task-005", "backend_instance_id": None, "status": "pending"},  # 无归属任务
    ]
    
    # 在所有Backend的MongoDB中插入任务数据
    for task in tasks:
        if task["backend_instance_id"]:  # 只有有归属的任务才插入
            backend1.mongo_db.social_tasks.insert_one(task)
            backend2.mongo_db.social_tasks.insert_one(task)
            backend3.mongo_db.social_tasks.insert_one(task)
    
    # 模拟所有Backend都收到所有任务的状态更新消息
    logger.info("--- 模拟广播消息处理 ---")
    
    for task in tasks:
        task_id = task["task_id"]
        task_data = {"status": task["status"], "progress": 50}
        
        logger.info(f"\n📡 广播任务{task_id}状态更新消息")
        
        # 所有Backend都收到消息
        await backend1.handle_task_message(task_id, task_data)
        await backend2.handle_task_message(task_id, task_data)
        await backend3.handle_task_message(task_id, task_data)
    
    # 统计结果
    logger.info("\n=== 处理结果统计 ===")
    logger.info(f"Backend-1 处理的任务: {backend1.processed_tasks}")
    logger.info(f"Backend-1 跳过的任务: {backend1.skipped_tasks}")
    logger.info(f"Backend-2 处理的任务: {backend2.processed_tasks}")
    logger.info(f"Backend-2 跳过的任务: {backend2.skipped_tasks}")
    logger.info(f"Backend-3 处理的任务: {backend3.processed_tasks}")
    logger.info(f"Backend-3 跳过的任务: {backend3.skipped_tasks}")
    
    # 验证结果
    success = True
    
    # Backend-1应该只处理task-001, task-003和task-005(无归属)
    expected_backend1 = ["task-001", "task-003", "task-005"]
    if set(backend1.processed_tasks) != set(expected_backend1):
        logger.error(f"❌ Backend-1处理任务不正确，期望: {expected_backend1}, 实际: {backend1.processed_tasks}")
        success = False
    
    # Backend-2应该只处理task-002和task-005(无归属)
    expected_backend2 = ["task-002", "task-005"]
    if set(backend2.processed_tasks) != set(expected_backend2):
        logger.error(f"❌ Backend-2处理任务不正确，期望: {expected_backend2}, 实际: {backend2.processed_tasks}")
        success = False
    
    # Backend-3应该只处理task-004和task-005(无归属)
    expected_backend3 = ["task-004", "task-005"]
    if set(backend3.processed_tasks) != set(expected_backend3):
        logger.error(f"❌ Backend-3处理任务不正确，期望: {expected_backend3}, 实际: {backend3.processed_tasks}")
        success = False
    
    if success:
        logger.info("✅ 任务归属过滤机制测试通过")
    else:
        logger.error("❌ 任务归属过滤机制测试失败")
    
    return success

async def test_cache_performance():
    """测试缓存性能"""
    logger.info("=== 测试缓存性能 ===")
    
    backend = MockRedisSyncService("backend-test")
    
    # 插入测试任务
    test_task = {"task_id": "cache-test", "backend_instance_id": "backend-test"}
    backend.mongo_db.social_tasks.insert_one(test_task)
    
    # 第一次查询（从MongoDB）
    start_time = time.time()
    owner1 = await backend._get_task_owner("cache-test")
    first_query_time = time.time() - start_time
    
    # 第二次查询（从Redis缓存）
    start_time = time.time()
    owner2 = await backend._get_task_owner("cache-test")
    second_query_time = time.time() - start_time
    
    logger.info(f"第一次查询时间: {first_query_time:.4f}秒 (MongoDB)")
    logger.info(f"第二次查询时间: {second_query_time:.4f}秒 (Redis缓存)")
    logger.info(f"缓存加速比: {first_query_time/second_query_time:.2f}x")
    
    # 验证结果一致性
    if owner1 == owner2 == "backend-test":
        logger.info("✅ 缓存性能测试通过")
        return True
    else:
        logger.error(f"❌ 缓存结果不一致: {owner1} vs {owner2}")
        return False

async def test_no_ownership_tasks():
    """测试无归属任务的处理"""
    logger.info("=== 测试无归属任务处理 ===")
    
    backend = MockRedisSyncService("backend-test")
    
    # 测试不存在的任务
    owner = await backend._get_task_owner("non-existent-task")
    if owner is None:
        logger.info("✅ 不存在的任务返回None")
    else:
        logger.error(f"❌ 不存在的任务应该返回None，实际返回: {owner}")
        return False
    
    # 测试无归属字段的任务
    task_without_owner = {"task_id": "no-owner-task", "status": "pending"}
    backend.mongo_db.social_tasks.insert_one(task_without_owner)
    
    owner = await backend._get_task_owner("no-owner-task")
    if owner is None:
        logger.info("✅ 无归属字段的任务返回None")
    else:
        logger.error(f"❌ 无归属字段的任务应该返回None，实际返回: {owner}")
        return False
    
    # 验证无归属任务可以被任何Backend处理
    result = await backend.handle_task_message("no-owner-task", {"status": "running"})
    if result:
        logger.info("✅ 无归属任务可以被任何Backend处理")
        return True
    else:
        logger.error("❌ 无归属任务应该可以被任何Backend处理")
        return False

async def main():
    """主测试函数"""
    logger.info("开始测试Backend任务归属机制")
    
    success_count = 0
    total_tests = 3
    
    # 测试任务归属过滤
    if await test_task_ownership_filtering():
        success_count += 1
    
    # 测试缓存性能
    if await test_cache_performance():
        success_count += 1
    
    # 测试无归属任务处理
    if await test_no_ownership_tasks():
        success_count += 1
    
    # 总结
    logger.info(f"\n=== 测试总结 ===")
    logger.info(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！Backend任务归属机制工作正常")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        exit(1)
