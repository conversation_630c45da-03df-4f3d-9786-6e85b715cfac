#!/usr/bin/env python3
"""
测试任务执行时Core服务选择的修复
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.schemas.social_repository import SocialDatabaseService
from app.config.database import DatabaseConfig
from app.api.task import start_core_task_async
import motor.motor_asyncio

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_core_service_selection():
    """测试Core服务选择逻辑"""
    try:
        # 初始化数据库连接
        db_config = DatabaseConfig()
        client = motor.motor_asyncio.AsyncIOMotorClient(db_config.mongodb_url)
        db = client[db_config.database_name]
        db_service = SocialDatabaseService(db)
        
        # 创建测试任务数据
        test_task_data = {
            "task_id": "test_core_service_task",
            "platform_id": "youtube",
            "account_id": "test_account",
            "device_id": "test_device",
            "content_path": "/test/path",
            "task_type": "single",
            "core_service_id": "test_core_service_123",  # 指定Core服务ID
            "metadata": {
                "test_field": "test_value"
            }
        }
        
        logger.info("=== 测试Core服务选择修复 ===")
        logger.info(f"测试任务数据: {test_task_data}")
        
        # 保存测试任务到数据库
        await db_service.create_task(test_task_data)
        logger.info("✅ 测试任务已保存到数据库")
        
        # 从数据库读取任务（模拟任务启动时的场景）
        task_from_db = await db_service.db.social_tasks.find_one({"task_id": "test_core_service_task"})
        if not task_from_db:
            logger.error("❌ 无法从数据库读取测试任务")
            return
            
        logger.info(f"从数据库读取的任务数据: {task_from_db}")
        
        # 检查core_service_id是否被正确保存和读取
        if "core_service_id" in task_from_db:
            logger.info(f"✅ core_service_id已正确保存到数据库: {task_from_db['core_service_id']}")
        else:
            logger.error("❌ core_service_id未保存到数据库")
            
        # 测试任务启动逻辑（不实际启动，只测试参数传递）
        logger.info("=== 测试任务启动时的Core服务选择 ===")
        
        # 模拟enhanced_task_data的创建过程
        enhanced_task_data = dict(task_from_db)
        
        # 检查core_service_id是否在enhanced_task_data中
        core_service_id = enhanced_task_data.get("core_service_id")
        if core_service_id:
            logger.info(f"✅ enhanced_task_data中包含core_service_id: {core_service_id}")
            logger.info("✅ 任务启动时将使用指定的Core服务")
        else:
            logger.error("❌ enhanced_task_data中缺少core_service_id")
            logger.error("❌ 任务启动时将使用默认Core服务（这是问题所在）")
            
        # 清理测试数据
        await db_service.db.social_tasks.delete_one({"task_id": "test_core_service_task"})
        logger.info("🧹 测试数据已清理")
        
        logger.info("=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    asyncio.run(test_core_service_selection())
