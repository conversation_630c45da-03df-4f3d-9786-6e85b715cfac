#!/usr/bin/env python3
"""
修复当前运行中任务的归属信息
为没有backend_instance_id的运行中任务设置归属
"""

import pymongo
import redis
import sys
import os

def fix_running_tasks_ownership():
    """修复运行中任务的归属信息"""
    try:
        # 连接MongoDB
        client = pymongo.MongoClient('mongodb://***************:27017/')
        db = client['thunderhub']
        
        # 连接Redis
        redis_client = redis.Redis.from_url('redis://***************:6379/1')
        
        # 查找所有运行中的任务
        running_tasks = list(db.social_tasks.find({
            "status": {"$in": ["running", "pending"]},
            "$or": [
                {"backend_instance_id": {"$exists": False}},
                {"backend_instance_id": None},
                {"backend_instance_id": ""}
            ]
        }))
        
        print(f"找到 {len(running_tasks)} 个需要修复归属的运行中任务")
        
        if not running_tasks:
            print("✅ 所有运行中任务都已有归属信息")
            return
        
        # 设置默认的Backend实例ID（当前活跃的实例）
        default_backend_id = "backend-a08e1764"  # 从日志中看到的当前实例ID
        
        for task in running_tasks:
            task_id = task['task_id']
            task_type = task.get('task_type', 'unknown')
            status = task.get('status', 'unknown')
            
            print(f"\n=== 修复任务 {task_id} ===")
            print(f"任务类型: {task_type}")
            print(f"状态: {status}")
            print(f"当前backend_instance_id: {task.get('backend_instance_id', '未设置')}")
            
            # 更新MongoDB中的任务归属
            update_result = db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {
                    "backend_instance_id": default_backend_id,
                    "started_by": default_backend_id
                }}
            )
            
            if update_result.modified_count > 0:
                print(f"✅ 已更新MongoDB中的任务归属: {default_backend_id}")
                
                # 缓存到Redis
                cache_key = f"task:{task_id}:owner"
                redis_client.setex(cache_key, 3600, default_backend_id)
                print(f"✅ 已缓存到Redis: {cache_key} = {default_backend_id}")
            else:
                print(f"⚠️ MongoDB更新失败或无变化")
        
        print(f"\n🎉 修复完成！共处理 {len(running_tasks)} 个任务")
        
        # 验证修复结果
        print("\n=== 验证修复结果 ===")
        for task in running_tasks:
            task_id = task['task_id']
            updated_task = db.social_tasks.find_one({"task_id": task_id})
            if updated_task and updated_task.get('backend_instance_id') == default_backend_id:
                print(f"✅ 任务 {task_id} 归属已正确设置")
            else:
                print(f"❌ 任务 {task_id} 归属设置失败")
        
        client.close()
        redis_client.close()
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False
    
    return True

def check_current_tasks():
    """检查当前任务的归属情况"""
    try:
        # 连接MongoDB
        client = pymongo.MongoClient('mongodb://***************:27017/')
        db = client['thunderhub']
        
        # 查看特定任务
        task_ids = [
            '8fb59f78-45b1-44ec-98d2-7914431fda10',
            '1b30cd4d-9c3d-45a0-a145-635cc5bdaa66'
        ]
        
        print("=== 检查特定任务归属 ===")
        for task_id in task_ids:
            task = db.social_tasks.find_one({'task_id': task_id})
            if task:
                print(f"\n任务 {task_id}:")
                print(f"  任务类型: {task.get('task_type')}")
                print(f"  状态: {task.get('status')}")
                print(f"  Backend实例ID: {task.get('backend_instance_id', '❌ 未设置')}")
                print(f"  启动者: {task.get('started_by', '❌ 未设置')}")
                print(f"  父任务ID: {task.get('parent_task_id', '无')}")
            else:
                print(f"❌ 未找到任务: {task_id}")
        
        # 统计所有任务的归属情况
        print(f"\n=== 任务归属统计 ===")
        
        total_tasks = db.social_tasks.count_documents({})
        print(f"总任务数: {total_tasks}")
        
        tasks_with_ownership = db.social_tasks.count_documents({
            "backend_instance_id": {"$exists": True, "$ne": None, "$ne": ""}
        })
        print(f"有归属信息的任务: {tasks_with_ownership}")
        
        tasks_without_ownership = total_tasks - tasks_with_ownership
        print(f"无归属信息的任务: {tasks_without_ownership}")
        
        running_tasks_without_ownership = db.social_tasks.count_documents({
            "status": {"$in": ["running", "pending"]},
            "$or": [
                {"backend_instance_id": {"$exists": False}},
                {"backend_instance_id": None},
                {"backend_instance_id": ""}
            ]
        })
        print(f"运行中但无归属的任务: {running_tasks_without_ownership}")
        
        client.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "fix":
        print("开始修复运行中任务的归属信息...")
        success = fix_running_tasks_ownership()
        if not success:
            sys.exit(1)
    else:
        print("检查当前任务归属情况...")
        check_current_tasks()
        print("\n如需修复运行中任务的归属，请运行: python fix_running_tasks_ownership.py fix")
