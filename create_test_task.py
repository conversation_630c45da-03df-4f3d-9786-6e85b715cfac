#!/usr/bin/env python3
"""
创建测试任务并测试工作流API
"""

import asyncio
import requests
import json
from motor.motor_asyncio import AsyncIOMotorClient

async def create_test_task():
    """创建测试任务"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient("mongodb://***************:27017")
        db = client["social_media_automation"]
        
        # 创建测试任务
        test_task = {
            "task_id": "test_workflow_real_123",
            "platform_id": "youtube",
            "workflow_name": "YouTube短视频上传",
            "status": "pending",
            "metadata": {
                "contentType": "shorts",
                "title": "测试短视频",
                "description": "这是一个测试短视频"
            },
            "created_at": "2024-01-01T10:00:00Z"
        }
        
        # 删除可能存在的旧测试任务
        await db.social_tasks.delete_many({"task_id": "test_workflow_real_123"})
        
        # 插入新的测试任务
        result = await db.social_tasks.insert_one(test_task)
        print(f"✅ 创建测试任务成功: {result.inserted_id}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试任务失败: {str(e)}")
        return False

def test_workflow_api():
    """测试工作流API"""
    try:
        task_id = "test_workflow_real_123"
        url = f"http://localhost:8000/api/v1/workflow/{task_id}/workflow"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功")
            print(f"数据源: {data.get('source', 'unknown')}")
            
            if data.get('success') and data.get('data'):
                workflow_data = data['data']
                print(f"工作流名称: {workflow_data.get('workflow_name')}")
                print(f"总步骤数: {workflow_data.get('total_steps')}")
                
                steps = workflow_data.get('steps', [])
                print(f"步骤详情:")
                for i, step in enumerate(steps[:5]):  # 只显示前5个步骤
                    name = step.get('name', 'Unknown')
                    action = step.get('action', 'unknown')
                    print(f"  步骤 {i+1}: {name} (Action: {action})")
                
                # 检查是否是真实配置
                if len(steps) > 5 and any(step.get('action') for step in steps):
                    print(f"✅ 获取到真实的工作流配置！")
                    return True
                else:
                    print(f"⚠️ 可能仍然是模拟数据")
                    return False
            else:
                print(f"❌ API返回数据格式错误")
                return False
        else:
            print(f"❌ API调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

async def cleanup_test_task():
    """清理测试任务"""
    try:
        client = AsyncIOMotorClient("mongodb://***************:27017")
        db = client["social_media_automation"]
        
        result = await db.social_tasks.delete_many({"task_id": "test_workflow_real_123"})
        print(f"✅ 清理测试任务成功，删除了 {result.deleted_count} 个任务")
        
        await client.close()
        
    except Exception as e:
        print(f"⚠️ 清理测试任务失败: {str(e)}")

async def main():
    """主函数"""
    print("开始测试工作流配置获取功能")
    
    # 1. 创建测试任务
    print("1. 创建测试任务")
    if not await create_test_task():
        print("创建测试任务失败，终止测试")
        return
    
    # 等待一下确保任务已保存
    await asyncio.sleep(1)
    
    # 2. 测试API
    print("2. 测试工作流配置API")
    success = test_workflow_api()
    
    # 3. 清理测试数据
    print("3. 清理测试数据")
    await cleanup_test_task()
    
    # 4. 输出结果
    if success:
        print("🎉 测试成功！Backend能够正确获取真实工作流配置")
    else:
        print("❌ 测试失败！需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
