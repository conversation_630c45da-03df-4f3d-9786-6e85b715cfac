<template>
  <el-dialog
    v-model="visible"
    title="📋 任务详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="task" class="task-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(task.status)">
              {{ getStatusText(task.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="平台">{{ task.platform_name }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ task.account_name }}</el-descriptions-item>
          <el-descriptions-item label="设备">{{ task.device_id }}</el-descriptions-item>
          <el-descriptions-item label="Core服务">
            <el-tag
              :type="getCoreServiceTagType(task.core_service_id)"
              size="small"
              effect="plain"
            >
              {{ getCoreServiceName(task.core_service_id) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">{{ task.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="内容路径" :span="2">{{ task.content_path }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(task.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(task.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间" v-if="task.end_time">{{ formatTime(task.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="工作流类型" v-if="task.workflow_name">{{ task.workflow_name }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 错误信息 -->
      <div v-if="task.status === 'failed'" class="error-info" style="margin-top: 20px;">
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header error">
              <el-icon class="error-icon"><Warning /></el-icon>
              <span>错误信息</span>
            </div>
          </template>
          
          <el-alert
            type="error"
            :closable="false"
            style="margin-bottom: 10px;"
          >
            <template #title>
              <div class="error-summary">
                <div><strong>任务执行失败</strong></div>
                <div style="margin-top: 5px; font-size: 14px;">
                  {{ getErrorSummary() }}
                </div>
              </div>
            </template>
          </el-alert>
        </el-card>
      </div>

      <!-- 实时日志 -->
      <div class="task-logs" style="margin-top: 20px;">
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon class="log-icon"><Document /></el-icon>
              <span>执行日志</span>
              <div class="log-stats">
                <el-tag v-if="getLogStats().error > 0" type="danger" size="small">
                  错误: {{ getLogStats().error }}
                </el-tag>
                <el-tag v-if="getLogStats().warning > 0" type="warning" size="small" style="margin-left: 5px;">
                  警告: {{ getLogStats().warning }}
                </el-tag>
                <el-tag type="info" size="small" style="margin-left: 5px;">
                  总计: {{ taskLogs.length }}
                </el-tag>
              </div>
            </div>
          </template>
          
          <div class="logs-container">
            <el-timeline v-if="taskLogs.length > 0">
              <el-timeline-item
                v-for="(log, index) in taskLogs"
                :key="index"
                :timestamp="formatTime(log.timestamp)"
                :type="getLogType(log.level)"
                size="small"
                :class="{ 'error-log-item': log.level === 'error' }"
              >
                <div class="log-content">
                  <span class="log-level" :class="`log-level-${log.level}`">
                    [{{ log.level.toUpperCase() }}]
                  </span>
                  <span class="log-message" :class="{ 'error-message': log.level === 'error' }">
                    {{ log.message }}
                  </span>
                </div>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无日志" />
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="refreshLogs">刷新日志</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Warning, Document } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  task: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const taskLogs = ref([])

// 监听任务变化，获取日志
watch(() => props.task, async (newTask) => {
  if (newTask) {
    console.log('任务变化，准备获取日志:', newTask.id, '弹窗状态:', visible.value)
    await fetchTaskLogs(newTask.id)
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(visible, async (isVisible) => {
  console.log('弹窗状态变化:', isVisible, '任务:', props.task?.id)
  if (isVisible && props.task) {
    await fetchTaskLogs(props.task.id)
  }
})

// 获取任务日志
const fetchTaskLogs = async (taskId) => {
  try {
    console.log('🔍 开始获取任务日志:', taskId)
    console.log('📡 请求URL:', `/api/tasks/detail/${taskId}/logs?limit=100&offset=0`)

    const response = await fetch(`/api/tasks/detail/${taskId}/logs?limit=100&offset=0`)

    console.log('📨 响应状态:', response.status, response.statusText)
    console.log('📨 响应头:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('📋 任务日志响应数据:', data)
    console.log('📋 日志数量:', data.logs?.length || 0)
    console.log('📋 总计:', data.total || 0)
    console.log('📋 错误信息:', data.error || '无')

    if (data && data.logs) {
      taskLogs.value = data.logs
      console.log('✅ 日志数据已设置到taskLogs.value，当前长度:', taskLogs.value.length)
    } else {
      taskLogs.value = []
      console.log('⚠️ 没有日志数据，设置为空数组')
    }

  } catch (error) {
    console.error('❌ 获取任务日志失败:', error)
    ElMessage.warning('获取任务日志失败')
    taskLogs.value = []
  }
}

// 刷新日志
const refreshLogs = () => {
  if (props.task) {
    fetchTaskLogs(props.task.id)
  }
}

// 工具函数
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

const getStatusType = (status: string) => {
  const statusMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'canceled': 'info',
    'paused': 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '等待中',
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败',
    'canceled': '已取消',
    'paused': '已暂停'
  }
  return statusMap[status] || status
}

const getLogType = (level: string) => {
  const typeMap = {
    'error': 'danger',
    'warning': 'warning',
    'info': 'primary',
    'success': 'success'
  }
  return typeMap[level] || 'primary'
}

const getLogStats = () => {
  const stats = {
    error: 0,
    warning: 0,
    info: 0,
    success: 0
  }
  
  taskLogs.value.forEach(log => {
    if (stats.hasOwnProperty(log.level)) {
      stats[log.level]++
    }
  })
  
  return stats
}

const getErrorSummary = () => {
  if (!props.task || props.task.status !== 'failed') {
    return ''
  }

  const errorLogs = taskLogs.value.filter(log => log.level === 'error')
  if (errorLogs.length === 0) {
    return '任务执行过程中发生未知错误'
  }

  const lastError = errorLogs[errorLogs.length - 1]
  let errorMsg = lastError.message

  if (errorLogs.length > 1) {
    errorMsg += ` (共${errorLogs.length}个错误)`
  }

  return errorMsg
}

// 🔧 新增：获取Core服务名称
const getCoreServiceName = (coreServiceId) => {
  if (!coreServiceId || coreServiceId === 'default') {
    return '默认服务'
  }
  return `Core-${coreServiceId}`
}

// 🔧 新增：获取Core服务标签类型
const getCoreServiceTagType = (coreServiceId) => {
  if (!coreServiceId || coreServiceId === 'default') {
    return 'info'
  }
  // 根据Core服务ID生成不同颜色
  const colors = ['primary', 'success', 'warning', 'danger']
  const hash = coreServiceId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  return colors[Math.abs(hash) % colors.length]
}
</script>

<style scoped>
.task-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-card {
  border: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.card-header > span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #409eff;
}

.error-icon {
  color: #f56c6c;
}

.log-icon {
  color: #909399;
}

.card-header.error {
  color: #f56c6c;
}

.log-stats {
  display: flex;
  align-items: center;
  gap: 5px;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
}

.error-log-item {
  background-color: #fef0f0;
  border-left: 3px solid #f56c6c;
  padding-left: 10px;
  margin-left: -10px;
  border-radius: 4px;
}

.log-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.log-level {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  margin-right: 8px;
}

.log-level-error {
  background-color: #ffebee;
  color: #c62828;
}

.log-level-warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.log-level-info {
  background-color: #e1f5fe;
  color: #0277bd;
}

.log-level-success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.log-message {
  word-break: break-word;
  line-height: 1.4;
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
}

.error-summary {
  line-height: 1.5;
}
</style>
