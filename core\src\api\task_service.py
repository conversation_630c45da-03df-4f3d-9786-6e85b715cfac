"""
任务服务gRPC实现
"""

import logging
import asyncio
import datetime
from typing import Dict, List, Any, Optional

from src.main_service import CoreMainService
from src.services.task_executor import TaskExecutor
from src.api import task_pb2
from src.api import task_pb2_grpc

logger = logging.getLogger(__name__)

class TaskServiceImpl(task_pb2_grpc.TaskServiceServicer):
    """任务服务gRPC实现类"""
    
    def __init__(self, main_service: CoreMainService):
        """初始化任务服务
        
        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.task_executor = TaskExecutor(main_service)
        logger.info("任务服务gRPC实现初始化")
    
    async def CreateTask(self, request, context):
        """创建任务"""
        try:
            logger.info(f"📥 gRPC收到创建任务请求: {request.task_id}")
            logger.info(f"📋 请求详情: task_type={request.task_type}, platform_id={request.platform_id}")
            logger.info(f"📋 请求参数数量: {len(request.params) if request.params else 0}")

            # 构建任务数据
            task_data = {
                "task_id": request.task_id,
                "task_type": request.task_type,  # 🔧 重要修复：添加任务类型字段
                "platform_id": request.platform_id,
                "account_id": request.account_id,
                "device_id": request.device_id,
                "content_path": request.content_path,
                "status": "pending",
                "created_at": datetime.datetime.now().isoformat()
            }

            # 添加可选字段
            if request.workflow_id:
                task_data["workflow_id"] = request.workflow_id
                logger.info(f"📋 添加workflow_id: {request.workflow_id}")

            # 添加任务参数
            if request.params:
                task_data["params"] = {}
                for key, value in request.params.items():
                    task_data["params"][key] = value
                logger.info(f"📋 添加参数: {len(request.params)}个")

            logger.info(f"📋 完整任务数据: {task_data}")

            # 创建任务
            logger.info(f"🔄 调用TaskExecutor创建任务: {request.task_id}")
            success = await self.task_executor.create_task(task_data)
            logger.info(f"📋 TaskExecutor创建结果: {success}")

            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = request.task_id

            if not success:
                response.error = "创建任务失败"
                logger.error(f"❌ 创建任务失败: {request.task_id}")
            else:
                logger.info(f"✅ gRPC创建任务成功: {request.task_id}")

            return response

        except Exception as e:
            logger.error(f"❌ gRPC创建任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"创建任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def StartTask(self, request, context):
        """开始执行任务"""
        try:
            task_id = request.task_id
            logger.info(f"📥 gRPC收到开始执行任务请求: '{task_id}'")
            logger.info(f"📋 请求对象: {request}")
            logger.info(f"📋 task_id类型: {type(task_id)}, 长度: {len(task_id) if task_id else 'None'}")
            logger.info(f"📋 task_id repr: {repr(task_id)}")

            # 开始执行任务
            logger.info(f"🔄 调用TaskExecutor启动任务: '{task_id}'")
            success = await self.task_executor.start_task(task_id)
            logger.info(f"📋 TaskExecutor启动结果: {success}")

            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = task_id

            if not success:
                response.error = f"任务不存在"
                logger.error(f"❌ gRPC启动任务失败: {task_id} - 任务不存在")
            else:
                logger.info(f"✅ gRPC启动任务成功: {task_id}")

            return response

        except Exception as e:
            logger.error(f"❌ gRPC启动任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"开始执行任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def PauseTask(self, request, context):
        """暂停任务"""
        try:
            task_id = request.task_id
            logger.info(f"收到暂停任务请求: {task_id}")
            
            # 暂停任务
            success = await self.task_executor.pause_task(task_id)
            
            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = task_id
            
            if not success:
                response.error = f"暂停任务{task_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"暂停任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"暂停任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def CancelTask(self, request, context):
        """取消任务"""
        try:
            task_id = request.task_id
            logger.info(f"收到取消任务请求: {task_id}")
            
            # 取消任务
            success = await self.task_executor.cancel_task(task_id)
            
            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = task_id
            
            if not success:
                response.error = f"取消任务{task_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"取消任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"取消任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def GetTaskStatus(self, request, context):
        """获取任务状态"""
        try:
            task_id = request.task_id
            logger.info(f"收到获取任务状态请求: {task_id}")
            
            # 获取任务状态
            status = await self.task_executor.get_task_status(task_id)
            
            if not status:
                context.set_code(5)  # NOT_FOUND
                context.set_details(f"任务{task_id}不存在")
                return task_pb2.TaskStatusResponse()
            
            # 构建响应
            response = task_pb2.TaskStatusResponse()
            response.task_id = task_id
            response.status = status.get("status", "unknown")
            response.progress = status.get("progress", 0)
            response.start_time = status.get("start_time", "")
            response.estimated_end_time = status.get("estimated_end_time", "")
            
            # 设备使用情况
            device_usage = status.get("device_usage", {})
            response.device_usage.cpu = device_usage.get("cpu", 0)
            response.device_usage.memory = device_usage.get("memory", 0)
            response.device_usage.network = device_usage.get("network", "未知")
            
            # 最近日志
            logs = status.get("logs", [])
            for log in logs:
                log_entry = response.logs.add()
                log_entry.message = log.get("message", "")
                log_entry.level = log.get("level", "info")
                log_entry.timestamp = log.get("timestamp", "")
            
            return response
            
        except Exception as e:
            logger.error(f"获取任务状态异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取任务状态失败: {str(e)}")
            return task_pb2.TaskStatusResponse()
    
    async def GetTaskLogs(self, request, context):
        """获取任务日志"""
        try:
            task_id = request.task_id
            logger.info(f"收到获取任务日志请求: {task_id}")
            
            # 获取任务日志
            logs = await self.task_executor.get_task_logs(task_id)
            
            # 构建响应
            response = task_pb2.TaskLogsResponse()
            response.task_id = task_id
            
            for log in logs:
                log_entry = response.logs.add()
                log_entry.message = log.get("message", "")
                log_entry.level = log.get("level", "info")
                log_entry.timestamp = log.get("timestamp", "")
            
            return response
            
        except Exception as e:
            logger.error(f"获取任务日志异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取任务日志失败: {str(e)}")
            return task_pb2.TaskLogsResponse()

    async def GetWorkflowConfig(self, request, context):
        """获取工作流配置"""
        try:
            platform_id = request.platform_id
            content_type = request.content_type
            logger.info(f"收到获取工作流配置请求: platform_id={platform_id}, content_type={content_type}")

            # 根据平台和内容类型确定工作流配置文件路径
            workflow_config = await self._load_workflow_config(platform_id, content_type)

            if not workflow_config:
                context.set_code(5)  # NOT_FOUND
                context.set_details(f"未找到工作流配置: platform_id={platform_id}, content_type={content_type}")
                return task_pb2.WorkflowConfigResponse(success=False, error="未找到工作流配置")

            # 构建响应
            response = task_pb2.WorkflowConfigResponse()
            response.success = True
            response.workflow_id = workflow_config.get("workflow", {}).get("id", "")
            response.workflow_name = workflow_config.get("workflow", {}).get("name", "")
            response.workflow_description = workflow_config.get("workflow", {}).get("description", "")
            response.workflow_version = workflow_config.get("workflow", {}).get("version", "1.0")

            # 添加工作流步骤
            steps = workflow_config.get("workflow", {}).get("steps", [])
            for step in steps:
                step_pb = response.steps.add()
                step_pb.id = step.get("id", "")
                step_pb.name = step.get("name", "")
                step_pb.description = step.get("description", "")
                step_pb.action = step.get("action", "")
                step_pb.required = step.get("required", True)
                step_pb.timeout = step.get("timeout", 30)
                step_pb.retry_count = step.get("retry_count", step.get("max_retries", 3))
                step_pb.wait_after = step.get("wait_after", 0)
                step_pb.condition = step.get("condition", "")
                step_pb.element = step.get("element", "")
                step_pb.notes = step.get("notes", "")

                # 将参数转换为JSON字符串
                import json
                parameters = step.get("parameters", step.get("params", {}))
                step_pb.parameters = json.dumps(parameters) if parameters else ""

            # 添加配置信息
            config = workflow_config.get("config", {})
            response.config = json.dumps(config) if config else ""

            logger.info(f"成功返回工作流配置: {response.workflow_name}, 步骤数: {len(steps)}")
            return response

        except Exception as e:
            logger.error(f"获取工作流配置异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取工作流配置失败: {str(e)}")
            return task_pb2.WorkflowConfigResponse(success=False, error=str(e))

    async def _load_workflow_config(self, platform_id: str, content_type: str) -> Optional[Dict[str, Any]]:
        """加载工作流配置文件

        Args:
            platform_id: 平台ID
            content_type: 内容类型

        Returns:
            工作流配置字典，如果未找到返回None
        """
        try:
            import os
            import yaml

            # 确定配置文件路径
            if platform_id == "youtube":
                if content_type == "shorts":
                    config_path = "config/platforms/youtube/workflows/shorts_upload.yaml"
                else:
                    config_path = "config/platforms/youtube/workflows/video_upload.yaml"
            else:
                # 其他平台的配置路径
                config_path = f"config/platforms/{platform_id}/workflows/{content_type}_upload.yaml"

            # 检查文件是否存在
            if not os.path.exists(config_path):
                logger.warning(f"工作流配置文件不存在: {config_path}")
                return None

            # 加载YAML配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            logger.info(f"成功加载工作流配置: {config_path}")
            return config

        except Exception as e:
            logger.error(f"加载工作流配置失败: {str(e)}", exc_info=True)
            return None
