#!/usr/bin/env python3
"""
检查任务继承关系
查看任务的父子关系和contentType继承情况
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId

async def check_task_inheritance():
    """检查任务继承关系"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient("mongodb://192.168.123.137:27017")
        db = client["social_media_automation"]
        
        # 查找任务
        task_id = "5d7c659e-947c-4066-a3a2-7f31088daf33"
        task = await db.social_tasks.find_one({"task_id": task_id})
        
        if not task:
            print(f"未找到任务: {task_id}")
            return
            
        print("=" * 60)
        print(f"当前任务信息:")
        print(f"任务ID: {task.get('task_id')}")
        print(f"任务类型: {task.get('task_type', '未设置')}")
        print(f"平台ID: {task.get('platform_id')}")
        print(f"工作流名称: {task.get('workflow_name', '未设置')}")
        print(f"状态: {task.get('status')}")
        print(f"父任务ID: {task.get('parent_task_id', '无')}")
        
        metadata = task.get('metadata', {})
        print(f"当前任务metadata: {metadata}")
        print(f"当前任务contentType: {metadata.get('contentType', '未设置')}")
        print(f"当前任务content_type: {metadata.get('content_type', '未设置')}")
        
        # 如果是子任务，查找父任务
        parent_task_id = task.get("parent_task_id")
        if parent_task_id:
            print("\n" + "=" * 60)
            print(f"父任务信息:")
            parent_task = await db.social_tasks.find_one({"task_id": parent_task_id})
            
            if parent_task:
                print(f"父任务ID: {parent_task.get('task_id')}")
                print(f"父任务类型: {parent_task.get('task_type', '未设置')}")
                print(f"父任务工作流名称: {parent_task.get('workflow_name', '未设置')}")
                print(f"父任务状态: {parent_task.get('status')}")
                
                parent_metadata = parent_task.get('metadata', {})
                print(f"父任务metadata: {parent_metadata}")
                print(f"父任务contentType: {parent_metadata.get('contentType', '未设置')}")
                print(f"父任务content_type: {parent_metadata.get('content_type', '未设置')}")
                
                # 分析继承情况
                print("\n" + "=" * 60)
                print("继承分析:")
                current_content_type = metadata.get('contentType') or metadata.get('content_type')
                parent_content_type = parent_metadata.get('contentType') or parent_metadata.get('content_type')
                
                if current_content_type:
                    print(f"✅ 当前任务有contentType: {current_content_type}")
                else:
                    print(f"❌ 当前任务缺少contentType")
                    if parent_content_type:
                        print(f"🔧 应该从父任务继承: {parent_content_type}")
                    else:
                        print(f"⚠️ 父任务也没有contentType")
                        
            else:
                print(f"❌ 未找到父任务: {parent_task_id}")
        else:
            print(f"✅ 这是主任务，无需继承")
            
        # 查找所有相关的子任务
        print("\n" + "=" * 60)
        print("相关子任务:")
        
        # 如果是主任务，查找子任务
        if task.get('task_type') == 'main':
            subtasks = await db.social_tasks.find({"parent_task_id": task_id}).to_list(None)
            print(f"找到 {len(subtasks)} 个子任务:")
            for i, subtask in enumerate(subtasks, 1):
                sub_metadata = subtask.get('metadata', {})
                sub_content_type = sub_metadata.get('contentType') or sub_metadata.get('content_type')
                print(f"  子任务{i}: {subtask.get('task_id')} - contentType: {sub_content_type or '未设置'}")
        
        # 如果是子任务，查找兄弟任务
        elif parent_task_id:
            siblings = await db.social_tasks.find({"parent_task_id": parent_task_id}).to_list(None)
            print(f"找到 {len(siblings)} 个兄弟任务:")
            for i, sibling in enumerate(siblings, 1):
                sib_metadata = sibling.get('metadata', {})
                sib_content_type = sib_metadata.get('contentType') or sib_metadata.get('content_type')
                is_current = sibling.get('task_id') == task_id
                marker = "👈 当前任务" if is_current else ""
                print(f"  任务{i}: {sibling.get('task_id')} - contentType: {sib_content_type or '未设置'} {marker}")
        
        # 推荐修复方案
        print("\n" + "=" * 60)
        print("修复建议:")
        
        current_content_type = metadata.get('contentType') or metadata.get('content_type')
        if not current_content_type and parent_task_id:
            parent_task = await db.social_tasks.find_one({"task_id": parent_task_id})
            if parent_task:
                parent_metadata = parent_task.get('metadata', {})
                parent_content_type = parent_metadata.get('contentType') or parent_metadata.get('content_type')
                if parent_content_type:
                    print(f"🔧 建议将当前任务的contentType设置为: {parent_content_type}")
                    print(f"🔧 这样工作流API就能获取到正确的配置文件")
                else:
                    print(f"⚠️ 父任务也没有contentType，需要手动设置")
        elif current_content_type:
            print(f"✅ 当前任务已有contentType: {current_content_type}")
        else:
            print(f"⚠️ 这是主任务，需要手动设置contentType")
            
        await client.close()
        
    except Exception as e:
        print(f"检查任务继承关系失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_task_inheritance())
