from typing import Optional, List, Dict, Any
import grpc
import logging
import os
import asyncio
import subprocess
from app.core.schemas.device_repository import DeviceConfigSchema, DeviceIDSchema
import httpx
import consul

# 导入gRPC生成的模块
# 注意：需要将core/src/api中的proto文件复制到backend并生成Python代码
from app.proto import device_pb2
from app.proto import device_pb2_grpc
from app.proto import task_pb2
from app.proto import task_pb2_grpc
from app.proto import file_pb2
from app.proto import file_pb2_grpc

logger = logging.getLogger(__name__)

class DeviceServiceClient:
    """设备服务gRPC客户端"""

    def __init__(self, host: str = 'localhost', port: int = 50051):
        """初始化设备服务客户端

        Args:
            host: Core服务主机
            port: Core服务端口
        """
        self.host = host
        self.port = port
        self.channel = None
        self.stub = None
        self._connect()

    def _connect(self):
        """连接到gRPC服务"""
        try:
            # 创建gRPC通道，设置连接选项
            options = [
                ('grpc.keepalive_time_ms', 120000),  # keepalive时间从60秒增加到120秒
                ('grpc.keepalive_timeout_ms', 30000),  # keepalive超时从10秒增加到30秒
                ('grpc.keepalive_permit_without_calls', False),  # 不允许无调用时发送keepalive
                ('grpc.http2.max_pings_without_data', 2),  # 限制无数据时的ping数量
                ('grpc.http2.min_time_between_pings_ms', 60000),  # ping间隔从30秒增加到60秒
                ('grpc.http2.min_ping_interval_without_data_ms', 600000),  # 无数据时ping间隔从5分钟增加到10分钟
                ('grpc.max_connection_idle_ms', 600000),  # 连接空闲时间从5分钟增加到10分钟
                ('grpc.max_connection_age_ms', 1200000),  # 连接最大存活从10分钟增加到20分钟
                ('grpc.max_connection_age_grace_ms', 60000)  # 连接关闭宽限期从30秒增加到60秒
            ]
            self.channel = grpc.insecure_channel(f"{self.host}:{self.port}", options=options)
            # 创建gRPC存根
            self.stub = device_pb2_grpc.DeviceServiceStub(self.channel)
            logger.info(f"已连接到Core服务: {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"连接Core服务失败: {str(e)}", exc_info=True)
            raise Exception(f"连接Core服务失败: {str(e)}") from e

    async def get_device_list(self, filter_str: str = "") -> List[Dict[str, Any]]:
        """获取设备列表

        Args:
            filter_str: 过滤条件

        Returns:
            设备列表
        """
        try:
            # 创建请求
            request = device_pb2.DeviceListRequest(filter=filter_str)
            # 调用gRPC方法
            response = self.stub.GetDeviceList(request)

            # 转换响应为Python字典列表
            devices = []
            for device in response.devices:
                devices.append({
                    "id": device.id,
                    "name": device.name,
                    "type": device.type,
                    "status": device.status,
                    "info": {
                        key: value for key, value in device.info.items()
                    }
                })

            return devices
        except grpc.RpcError as e:
            logger.error(f"获取设备列表失败: {e.details()}", exc_info=True)
            raise Exception(f"获取设备列表失败: {e.details()}") from e

    async def add_device(self, config: Dict[str, Any]) -> bool:
        """添加设备

        Args:
            config: 设备配置

        Returns:
            是否成功
        """
        try:
            # 创建请求
            request = device_pb2.CreateDeviceRequest()
            request.name = config.get("name", "")
            request.type = config.get("type", "")

            # 添加配置参数
            for key, value in config.get("config", {}).items():
                if isinstance(value, str):
                    request.config[key] = value
                else:
                    request.config[key] = str(value)

            # 调用gRPC方法
            response = self.stub.CreateDevice(request)
            return response.success
        except grpc.RpcError as e:
            logger.error(f"添加设备失败: {e.details()}", exc_info=True)
            raise Exception(f"添加设备失败: {e.details()}") from e

    async def start_device(self, device_id: str) -> bool:
        """启动设备

        Args:
            device_id: 设备ID

        Returns:
            是否成功
        """
        try:
            # 创建请求
            request = device_pb2.DeviceRequest(device_id=device_id)
            # 调用gRPC方法，设置60秒超时
            response = self.stub.StartDevice(request, timeout=60.0)
            return response.success
        except grpc.RpcError as e:
            logger.error(f"启动设备失败: {e.details()}", exc_info=True)
            raise Exception(f"启动设备失败: {e.details()}") from e

    async def stop_device(self, device_id: str) -> bool:
        """停止设备

        Args:
            device_id: 设备ID

        Returns:
            是否成功
        """
        try:
            # 创建请求
            request = device_pb2.DeviceRequest(device_id=device_id)
            # 调用gRPC方法
            response = self.stub.StopDevice(request)
            return response.success
        except grpc.RpcError as e:
            logger.error(f"停止设备失败: {e.details()}", exc_info=True)
            raise Exception(f"停止设备失败: {e.details()}") from e

    async def restart_device(self, device_id: str) -> bool:
        """重启设备

        Args:
            device_id: 设备ID

        Returns:
            是否成功
        """
        try:
            # 创建请求
            request = device_pb2.DeviceRequest(device_id=device_id)
            # 调用gRPC方法
            response = self.stub.RestartDevice(request)
            return response.success
        except grpc.RpcError as e:
            logger.error(f"重启设备失败: {e.details()}", exc_info=True)
            raise Exception(f"重启设备失败: {e.details()}") from e

    async def execute_command(self, device_id: str, command: str, params: Dict[str, str] = None) -> Dict[str, Any]:
        """执行设备命令

        Args:
            device_id: 设备ID
            command: 命令
            params: 参数

        Returns:
            命令执行结果
        """
        try:
            # 创建请求
            request = device_pb2.CommandRequest(device_id=device_id, command=command)

            # 添加参数
            if params:
                for key, value in params.items():
                    request.params[key] = value

            # 调用gRPC方法
            response = self.stub.ExecuteCommand(request)

            # 返回结果
            result = {
                "success": response.success
            }

            if not response.success:
                result["error"] = response.error
            else:
                result["output"] = response.output

            return result
        except grpc.RpcError as e:
            logger.error(f"执行命令失败: {e.details()}", exc_info=True)
            raise Exception(f"执行命令失败: {e.details()}") from e

    async def close(self):
        """关闭连接"""
        if hasattr(self, 'channel') and self.channel:
            await self.channel.close()
            logger.info("已关闭Core服务连接")


class FileServiceClient:
    """文件服务gRPC客户端"""

    def __init__(self, host: str = 'localhost', port: int = 50051):
        """初始化文件服务客户端

        Args:
            host: Core服务主机
            port: Core服务端口
        """
        self.host = host
        self.port = port
        self.channel = None
        self.stub = None
        self._connect()

    def _connect(self):
        """连接到gRPC服务"""
        try:
            # 创建gRPC通道，设置连接选项
            options = [
                ('grpc.keepalive_time_ms', 120000),  # keepalive时间从60秒增加到120秒
                ('grpc.keepalive_timeout_ms', 30000),  # keepalive超时从10秒增加到30秒
                ('grpc.keepalive_permit_without_calls', False),  # 不允许无调用时发送keepalive
                ('grpc.http2.max_pings_without_data', 2),  # 限制无数据时的ping数量
                ('grpc.http2.min_time_between_pings_ms', 60000),  # ping间隔从30秒增加到60秒
                ('grpc.http2.min_ping_interval_without_data_ms', 600000),  # 无数据时ping间隔从5分钟增加到10分钟
                ('grpc.max_connection_idle_ms', 600000),  # 连接空闲时间从5分钟增加到10分钟
                ('grpc.max_connection_age_ms', 1200000),  # 连接最大存活从10分钟增加到20分钟
                ('grpc.max_connection_age_grace_ms', 60000)  # 连接关闭宽限期从30秒增加到60秒
            ]
            self.channel = grpc.insecure_channel(f"{self.host}:{self.port}", options=options)
            # 创建gRPC存根
            self.stub = file_pb2_grpc.FileServiceStub(self.channel)
            logger.info(f"已连接到Core文件服务: {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"连接Core文件服务失败: {str(e)}", exc_info=True)
            raise Exception(f"连接Core文件服务失败: {str(e)}") from e

    async def list_directory(self, path: str, filter_extensions: List[str] = None,
                           include_md5: bool = False, include_media_info: bool = False) -> Dict[str, Any]:
        """列出目录内容

        Args:
            path: 目录路径
            filter_extensions: 过滤扩展名列表（如 ["mp4", "avi"]）
            include_md5: 是否计算文件MD5哈希值
            include_media_info: 是否获取媒体文件信息

        Returns:
            目录内容
        """
        try:
            # 创建请求
            request = file_pb2.ListDirectoryRequest(
                path=path,
                include_md5=include_md5,
                include_media_info=include_media_info
            )

            # 添加过滤扩展名
            if filter_extensions:
                for ext in filter_extensions:
                    request.filter_extensions.append(ext)

            logger.info(f"调用Core服务列出目录: {path}")
            logger.info(f"过滤扩展名: {filter_extensions}, MD5: {include_md5}, 媒体信息: {include_media_info}")

            # 调用gRPC方法，设置适当的超时时间（MD5计算可能需要较长时间）
            timeout = 300.0 if include_md5 or include_media_info else 30.0
            response = self.stub.ListDirectory(request, timeout=timeout)

            # 转换响应为Python字典列表
            files = []
            for file_info in response.files:
                file_dict = {
                    "name": file_info.name,
                    "path": file_info.path,
                    "is_directory": file_info.is_directory,
                    "size": file_info.size,
                    "modified_time": file_info.modified_time,
                    "md5_hash": file_info.md5_hash if file_info.md5_hash else None,
                    "media_info": None
                }

                # 添加媒体信息（如果存在）
                if file_info.HasField('media_info'):
                    media_info = file_info.media_info
                    file_dict["media_info"] = {
                        "duration": media_info.duration,
                        "resolution": media_info.resolution,
                        "video_codec": media_info.video_codec,
                        "audio_codec": media_info.audio_codec,
                        "frame_rate": media_info.frame_rate,
                        "bitrate": media_info.bitrate
                    }

                files.append(file_dict)

            result = {
                "success": True,
                "files": files,
                "total_count": len(files)
            }

            logger.info(f"列出目录成功: 共 {len(files)} 个项目")
            return result

        except grpc.RpcError as e:
            logger.error(f"列出目录失败: {e.details()}", exc_info=True)
            return {
                "success": False,
                "error": f"列出目录失败: {e.details()}",
                "files": [],
                "total_count": 0
            }

    async def delete_files(self, file_paths: List[str]) -> Dict[str, Any]:
        """删除文件

        Args:
            file_paths: 要删除的文件路径列表

        Returns:
            删除结果
        """
        try:
            # 创建请求
            request = file_pb2.DeleteFilesRequest()

            # 初始化file_paths字段
            for file_path in file_paths:
                request.file_paths.append(file_path)

            logger.info(f"调用Core服务删除文件，数量: {len(file_paths)}")
            for file_path in file_paths:
                logger.info(f"  - {file_path}")

            # 调用gRPC方法
            response = self.stub.DeleteFiles(request)

            # 返回结果
            result = {
                "success": response.success,
                "deleted_count": response.deleted_count,
                "total_count": response.total_count,
                "errors": list(response.errors)
            }

            logger.info(f"删除文件完成: 成功删除 {response.deleted_count}/{response.total_count} 个文件")
            if response.errors:
                logger.warning(f"删除过程中有 {len(response.errors)} 个错误")

            return result
        except grpc.RpcError as e:
            logger.error(f"删除文件失败: {e.details()}", exc_info=True)
            raise Exception(f"删除文件失败: {e.details()}") from e

    async def move_files(self, operations: List[Dict[str, str]]) -> Dict[str, Any]:
        """移动文件

        Args:
            operations: 文件移动操作列表，每个操作包含source_path和target_path

        Returns:
            移动结果
        """
        try:
            # 创建请求
            request = file_pb2.MoveFilesRequest()

            # 添加移动操作
            for operation in operations:
                move_op = request.operations.add()
                move_op.source_path = operation["source_path"]
                move_op.target_path = operation["target_path"]

            logger.info(f"调用Core服务移动文件，操作数量: {len(operations)}")
            for operation in operations:
                logger.info(f"  - {operation['source_path']} -> {operation['target_path']}")

            # 调用gRPC方法
            response = self.stub.MoveFiles(request)

            # 返回结果
            result = {
                "success": response.success,
                "moved_count": response.moved_count,
                "total_count": response.total_count,
                "errors": list(response.errors)
            }

            logger.info(f"移动文件完成: 成功移动 {response.moved_count}/{response.total_count} 个文件")
            if response.errors:
                logger.warning(f"移动过程中有 {len(response.errors)} 个错误")

            return result

        except Exception as e:
            logger.error(f"调用Core服务移动文件失败: {str(e)}", exc_info=True)
            raise Exception(f"调用Core服务移动文件失败: {str(e)}") from e

    async def create_triple_video(self, folder_path: str, output_path: str,
                                video_duration_per_segment: int,
                                transition_duration: float,
                                output_quality: str) -> Dict[str, Any]:
        """创建三拼视频

        Args:
            folder_path: 文件夹路径
            output_path: 输出文件路径
            video_duration_per_segment: 每个视频片段播放时长（秒）
            transition_duration: 转场时长（秒）
            output_quality: 输出质量

        Returns:
            创建结果
        """
        try:
            # 创建请求
            request = file_pb2.CreateTripleVideoRequest(
                folder_path=folder_path,
                output_path=output_path,
                video_duration_per_segment=video_duration_per_segment,
                transition_duration=transition_duration,
                output_quality=output_quality
            )

            logger.info(f"调用Core服务创建三拼视频:")
            logger.info(f"  - 文件夹: {folder_path}")
            logger.info(f"  - 输出: {output_path}")
            logger.info(f"  - 每段时长: {video_duration_per_segment}秒")
            logger.info(f"  - 转场时长: {transition_duration}秒")
            logger.info(f"  - 输出质量: {output_quality}")

            # 调用gRPC方法，设置较长的超时时间（三拼视频创建可能需要很长时间）
            response = self.stub.CreateTripleVideo(request, timeout=600.0)  # 10分钟超时

            # 返回结果
            result = {
                "success": response.success,
                "output_file": response.output_file,
                "processed_videos": response.processed_videos,
                "error": response.error if not response.success else ""
            }

            if response.success:
                logger.info(f"三拼视频创建成功: {response.output_file}, 处理了 {response.processed_videos} 个视频")
            else:
                logger.error(f"三拼视频创建失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"创建三拼视频失败: {e.details()}", exc_info=True)
            raise Exception(f"创建三拼视频失败: {e.details()}") from e

    async def merge_videos(self, folder_path: str, target_duration_min: int,
                          target_duration_max: int, enable_transitions: bool,
                          output_quality: str, max_videos_per_merge: int) -> Dict[str, Any]:
        """合并视频

        Args:
            folder_path: 文件夹路径
            target_duration_min: 目标时长最小值（秒）
            target_duration_max: 目标时长最大值（秒）
            enable_transitions: 是否启用转场特效
            output_quality: 输出质量
            max_videos_per_merge: 每个合并视频最多包含的原视频数量

        Returns:
            合并结果
        """
        try:
            # 创建请求
            request = file_pb2.MergeVideosRequest(
                folder_path=folder_path,
                target_duration_min=target_duration_min,
                target_duration_max=target_duration_max,
                enable_transitions=enable_transitions,
                output_quality=output_quality,
                max_videos_per_merge=max_videos_per_merge
            )

            logger.info(f"调用Core服务合并视频:")
            logger.info(f"  - 文件夹: {folder_path}")
            logger.info(f"  - 目标时长: {target_duration_min}-{target_duration_max}秒")
            logger.info(f"  - 启用转场: {enable_transitions}")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 每组最大视频数: {max_videos_per_merge}")

            # 调用gRPC方法，设置较长的超时时间（视频合并可能需要很长时间）
            response = self.stub.MergeVideos(request, timeout=1200.0)  # 超时时间从10分钟增加到20分钟

            # 返回结果
            result = {
                "success": response.success,
                "output_files": list(response.output_files),
                "processed_videos": response.processed_videos,
                "successful_merges": response.successful_merges,
                "error": response.error if not response.success else ""
            }

            if response.success:
                logger.info(f"视频合并成功: 处理了 {response.processed_videos} 个视频，成功合并 {response.successful_merges} 组")
            else:
                logger.error(f"视频合并失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"合并视频失败: {e.details()}", exc_info=True)
            raise Exception(f"合并视频失败: {e.details()}") from e

    async def detect_watermark(self, video_path: str, detection_mode: str = 'auto',
                              template_path: str = '', detection_region: str = '',
                              sensitivity: float = 0.7, save_detection_result: bool = False) -> Dict[str, Any]:
        """检测视频水印

        Args:
            video_path: 视频文件路径
            detection_mode: 检测模式 (auto, template, region)
            template_path: 水印模板路径
            detection_region: 检测区域 (x,y,width,height)
            sensitivity: 检测敏感度 (0.0-1.0)
            save_detection_result: 是否保存检测结果图片

        Returns:
            检测结果
        """
        try:
            # 创建请求
            request = file_pb2.DetectWatermarkRequest(
                video_path=video_path,
                detection_mode=detection_mode,
                template_path=template_path,
                detection_region=detection_region,
                sensitivity=sensitivity,
                save_detection_result=save_detection_result
            )

            logger.info(f"调用Core服务检测视频水印:")
            logger.info(f"  - 视频路径: {video_path}")
            logger.info(f"  - 检测模式: {detection_mode}")
            logger.info(f"  - 敏感度: {sensitivity}")

            # 调用gRPC方法，设置适当的超时时间
            response = self.stub.DetectWatermark(request, timeout=120.0)  # 2分钟超时

            # 构建水印信息
            watermarks = []
            for wm in response.watermarks:
                watermarks.append({
                    "watermark_type": wm.watermark_type,
                    "position": wm.position,
                    "confidence": wm.confidence,
                    "description": wm.description,
                    "time_range": wm.time_range
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "watermark_detected": response.watermark_detected,
                "watermarks": watermarks,
                "detection_result_path": response.detection_result_path,
                "detection_time_ms": response.detection_time_ms
            }

            if response.success:
                logger.info(f"水印检测成功: 检测到 {len(watermarks)} 个水印，耗时 {response.detection_time_ms}ms")
            else:
                logger.error(f"水印检测失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"检测视频水印失败: {e.details()}", exc_info=True)
            raise Exception(f"检测视频水印失败: {e.details()}") from e

    async def remove_watermark(self, input_video_path: str, output_video_path: str,
                              removal_mode: str = 'auto', watermark_regions: List[str] = None,
                              inpaint_method: str = 'blur', output_quality: str = 'medium',
                              preserve_encoding: bool = False) -> Dict[str, Any]:
        """清除视频水印

        Args:
            input_video_path: 输入视频文件路径
            output_video_path: 输出视频文件路径
            removal_mode: 清除模式 (auto, manual, inpaint)
            watermark_regions: 水印区域列表 (x,y,width,height)
            inpaint_method: 修复算法 (blur, median, inpaint)
            output_quality: 输出质量 (high, medium, low)
            preserve_encoding: 是否保持原始编码

        Returns:
            清除结果
        """
        try:
            # 创建请求
            request = file_pb2.RemoveWatermarkRequest(
                input_video_path=input_video_path,
                output_video_path=output_video_path,
                removal_mode=removal_mode,
                inpaint_method=inpaint_method,
                output_quality=output_quality,
                preserve_encoding=preserve_encoding
            )

            # 添加水印区域
            if watermark_regions:
                for region in watermark_regions:
                    request.watermark_regions.append(region)

            logger.info(f"调用Core服务清除视频水印:")
            logger.info(f"  - 输入视频: {input_video_path}")
            logger.info(f"  - 输出视频: {output_video_path}")
            logger.info(f"  - 清除模式: {removal_mode}")
            logger.info(f"  - 水印区域数量: {len(watermark_regions) if watermark_regions else 0}")

            # 调用gRPC方法，设置较长的超时时间（水印清除可能需要很长时间）
            response = self.stub.RemoveWatermark(request, timeout=300.0)  # 5分钟超时

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "output_file_path": response.output_file_path,
                "processing_time_ms": response.processing_time_ms,
                "original_file_size": response.original_file_size,
                "output_file_size": response.output_file_size,
                "removed_watermarks_count": response.removed_watermarks_count
            }

            if response.success:
                logger.info(f"水印清除成功: {response.output_file_path}，耗时 {response.processing_time_ms}ms")
            else:
                logger.error(f"水印清除失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"清除视频水印失败: {e.details()}", exc_info=True)
            raise Exception(f"清除视频水印失败: {e.details()}") from e

    async def batch_process_watermark(self, input_folder_path: str, output_folder_path: str,
                                    process_mode: str = 'detect_and_remove',
                                    file_filters: List[str] = None, recursive: bool = False,
                                    max_concurrent: int = 3, detection_config: Dict[str, Any] = None,
                                    removal_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """批量处理视频水印

        Args:
            input_folder_path: 输入文件夹路径
            output_folder_path: 输出文件夹路径
            process_mode: 处理模式 (detect_only, remove_only, detect_and_remove)
            file_filters: 文件过滤器
            recursive: 是否递归处理子目录
            max_concurrent: 最大并发处理数
            detection_config: 检测配置
            removal_config: 清除配置

        Returns:
            批量处理结果
        """
        try:
            # 创建请求
            request = file_pb2.BatchProcessWatermarkRequest(
                input_folder_path=input_folder_path,
                output_folder_path=output_folder_path,
                process_mode=process_mode,
                recursive=recursive,
                max_concurrent=max_concurrent
            )

            # 添加文件过滤器
            if file_filters:
                for filter_pattern in file_filters:
                    request.file_filters.append(filter_pattern)
            else:
                request.file_filters.append("*.mp4")

            # 添加检测配置
            if detection_config:
                det_config = request.detection_config
                det_config.detection_mode = detection_config.get('detection_mode', 'auto')
                det_config.template_path = detection_config.get('template_path', '')
                det_config.detection_region = detection_config.get('detection_region', '')
                det_config.sensitivity = detection_config.get('sensitivity', 0.7)
                det_config.save_detection_result = detection_config.get('save_detection_result', False)

            # 添加清除配置
            if removal_config:
                rem_config = request.removal_config
                rem_config.removal_mode = removal_config.get('removal_mode', 'auto')
                rem_config.inpaint_method = removal_config.get('inpaint_method', 'blur')
                rem_config.output_quality = removal_config.get('output_quality', 'medium')
                rem_config.preserve_encoding = removal_config.get('preserve_encoding', False)

                # 添加水印区域
                watermark_regions = removal_config.get('watermark_regions', [])
                for region in watermark_regions:
                    rem_config.watermark_regions.append(region)

            logger.info(f"调用Core服务批量处理视频水印:")
            logger.info(f"  - 输入文件夹: {input_folder_path}")
            logger.info(f"  - 输出文件夹: {output_folder_path}")
            logger.info(f"  - 处理模式: {process_mode}")
            logger.info(f"  - 最大并发: {max_concurrent}")

            # 调用gRPC方法，设置很长的超时时间（批量处理可能需要很长时间）
            response = self.stub.BatchProcessWatermark(request, timeout=1800.0)  # 30分钟超时

            # 构建处理结果
            results = []
            for result_item in response.results:
                item = {
                    "file_path": result_item.file_path,
                    "status": result_item.status,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "detection_result": None,
                    "removal_result": None
                }

                # 添加检测结果
                if result_item.detection_result and result_item.detection_result.success:
                    watermarks = []
                    for wm in result_item.detection_result.watermarks:
                        watermarks.append({
                            "watermark_type": wm.watermark_type,
                            "position": wm.position,
                            "confidence": wm.confidence,
                            "description": wm.description
                        })

                    item["detection_result"] = {
                        "success": result_item.detection_result.success,
                        "watermark_detected": result_item.detection_result.watermark_detected,
                        "watermarks": watermarks,
                        "detection_time_ms": result_item.detection_result.detection_time_ms
                    }

                # 添加清除结果
                if result_item.removal_result and result_item.removal_result.success:
                    item["removal_result"] = {
                        "success": result_item.removal_result.success,
                        "output_file_path": result_item.removal_result.output_file_path,
                        "processing_time_ms": result_item.removal_result.processing_time_ms,
                        "original_file_size": result_item.removal_result.original_file_size,
                        "output_file_size": result_item.removal_result.output_file_size,
                        "removed_watermarks_count": result_item.removal_result.removed_watermarks_count
                    }

                results.append(item)

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "results": results,
                "total_files": response.total_files,
                "successful_files": response.successful_files,
                "failed_files": response.failed_files,
                "total_processing_time_ms": response.total_processing_time_ms
            }

            if response.success:
                logger.info(f"批量处理完成: 总计 {response.total_files} 个文件，成功 {response.successful_files} 个，失败 {response.failed_files} 个")
            else:
                logger.error(f"批量处理失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"批量处理视频水印失败: {e.details()}", exc_info=True)
            raise Exception(f"批量处理视频水印失败: {e.details()}") from e

    async def archive_published_files(self, folder_path: str, archive_folder_name: str = "已发布",
                                     platforms: List[str] = None, published_status: Dict[str, bool] = None) -> Dict[str, Any]:
        """归档已发布文件

        Args:
            folder_path: 文件夹路径
            archive_folder_name: 归档文件夹名称
            platforms: 要检查的平台列表
            published_status: MD5哈希值到发布状态的映射

        Returns:
            归档结果
        """
        try:
            # 创建请求
            request = file_pb2.ArchivePublishedFilesRequest(
                folder_path=folder_path,
                archive_folder_name=archive_folder_name
            )

            # 添加平台列表
            if platforms:
                for platform in platforms:
                    request.platforms.append(platform)

            # 添加发布状态映射
            if published_status:
                for md5_hash, is_published in published_status.items():
                    request.published_status[md5_hash] = is_published

            logger.info(f"调用Core服务归档已发布文件:")
            logger.info(f"  - 文件夹: {folder_path}")
            logger.info(f"  - 归档文件夹: {archive_folder_name}")
            logger.info(f"  - 平台数量: {len(platforms) if platforms else 0}")
            logger.info(f"  - 发布状态映射: {len(published_status) if published_status else 0} 个文件")

            # 调用gRPC方法，设置适当的超时时间
            response = self.stub.ArchivePublishedFiles(request, timeout=120.0)  # 2分钟超时

            # 返回结果
            result = {
                "success": response.success,
                "archived_count": response.archived_count,
                "skipped_count": response.skipped_count,
                "archive_folder": response.archive_folder,
                "archived_files": list(response.archived_files),
                "errors": list(response.errors)
            }

            if response.success:
                logger.info(f"归档已发布文件成功: 归档 {response.archived_count} 个文件，跳过 {response.skipped_count} 个文件")
            else:
                logger.error(f"归档已发布文件失败，有 {len(response.errors)} 个错误")

            return result
        except grpc.RpcError as e:
            logger.error(f"归档已发布文件失败: {e.details()}", exc_info=True)
            raise Exception(f"归档已发布文件失败: {e.details()}") from e

    async def rotate_videos(self, video_paths: List[str], rotation_angle: int,
                          output_quality: str = 'medium', overwrite_original: bool = False,
                          output_suffix: str = '_rotated') -> Dict[str, Any]:
        """旋转视频

        Args:
            video_paths: 要旋转的视频文件路径列表
            rotation_angle: 旋转角度（90: 向右90度, -90: 向左90度, 180: 180度）
            output_quality: 输出质量（high, medium, low）
            overwrite_original: 是否覆盖原文件
            output_suffix: 输出文件名后缀

        Returns:
            旋转结果
        """
        try:
            # 创建请求
            request = file_pb2.RotateVideosRequest(
                rotation_angle=rotation_angle,
                output_quality=output_quality,
                overwrite_original=overwrite_original,
                output_suffix=output_suffix
            )

            # 添加视频路径
            for video_path in video_paths:
                request.video_paths.append(video_path)

            logger.info(f"调用Core服务旋转视频:")
            logger.info(f"  - 视频文件数量: {len(video_paths)}")
            logger.info(f"  - 旋转角度: {rotation_angle}°")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 覆盖原文件: {overwrite_original}")
            logger.info(f"  - 输出后缀: {output_suffix}")

            # 调用gRPC方法，设置适当的超时时间（视频旋转可能需要较长时间）
            response = self.stub.RotateVideos(request, timeout=300.0)  # 5分钟超时

            # 构建旋转结果
            results = []
            for result_item in response.results:
                results.append({
                    "original_path": result_item.original_path,
                    "output_path": result_item.output_path,
                    "success": result_item.success,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "original_file_size": result_item.original_file_size,
                    "output_file_size": result_item.output_file_size
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "results": results,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count,
                "total_processing_time_ms": response.total_processing_time_ms
            }

            if response.success:
                logger.info(f"视频旋转完成: 成功 {response.successful_count} 个，失败 {response.failed_count} 个")
            else:
                logger.error(f"视频旋转失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"旋转视频失败: {e.details()}", exc_info=True)
            raise Exception(f"旋转视频失败: {e.details()}") from e

    async def accelerate_videos(self, video_paths: List[str], target_duration: int = 59,
                              output_quality: str = 'medium', overwrite_original: bool = False,
                              output_suffix: str = '_accelerated') -> Dict[str, Any]:
        """批量视频加速

        Args:
            video_paths: 要加速的视频文件路径列表
            target_duration: 目标时长（秒）
            output_quality: 输出质量（high, medium, low）
            overwrite_original: 是否覆盖原文件
            output_suffix: 输出文件名后缀

        Returns:
            加速结果
        """
        try:
            # 创建请求
            request = file_pb2.AccelerateVideosRequest()
            request.video_paths.extend(video_paths)
            request.target_duration = target_duration
            request.output_quality = output_quality
            request.overwrite_original = overwrite_original
            request.output_suffix = output_suffix

            logger.info(f"调用Core服务加速视频:")
            logger.info(f"  - 视频文件数量: {len(video_paths)}")
            logger.info(f"  - 目标时长: {target_duration}秒")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 覆盖原文件: {overwrite_original}")
            if not overwrite_original:
                logger.info(f"  - 输出到 accelerated_videos 文件夹，保持原文件名")

            # 调用gRPC方法，设置适当的超时时间（视频加速可能需要较长时间）
            response = self.stub.AccelerateVideos(request, timeout=600.0)  # 10分钟超时

            # 构建加速结果
            results = []
            for result_item in response.results:
                results.append({
                    "success": result_item.success,
                    "original_path": result_item.original_path,
                    "output_path": result_item.output_path,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "original_file_size": result_item.original_file_size,
                    "output_file_size": result_item.output_file_size,
                    "original_duration": result_item.original_duration,
                    "output_duration": result_item.output_duration,
                    "speed_factor": result_item.speed_factor
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "results": results,
                "total_processing_time_ms": response.total_processing_time_ms,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count
            }

            if response.success:
                logger.info(f"视频加速成功，处理了 {response.successful_count} 个视频，失败 {response.failed_count} 个")
            else:
                logger.error(f"视频加速失败: {response.error}")

            return result
        except Exception as e:
            logger.error(f"加速视频失败: {str(e)}", exc_info=True)
            raise Exception(f"加速视频失败: {str(e)}") from e

    async def clip_videos(self, video_paths: List[str], clip_mode: str,
                         segment_count: int = 0, segment_duration: float = 0.0,
                         buffer_duration: float = 2.0, output_quality: str = "medium",
                         output_folder: str = "", filename_template: str = "",
                         preserve_audio_quality: bool = True, max_concurrent: int = 3,
                         volume_sensitivity: float = 0.5, min_segment_duration: float = 5.0) -> Dict[str, Any]:
        """智能视频裁剪"""
        try:
            # 创建请求
            request = file_pb2.ClipVideosRequest()
            request.video_paths.extend(video_paths)
            request.clip_mode = clip_mode
            request.segment_count = segment_count
            request.segment_duration = segment_duration
            request.buffer_duration = buffer_duration
            request.output_quality = output_quality
            request.output_folder = output_folder
            request.filename_template = filename_template
            request.preserve_audio_quality = preserve_audio_quality
            request.max_concurrent = max_concurrent
            request.volume_sensitivity = volume_sensitivity
            request.min_segment_duration = min_segment_duration

            logger.info(f"调用Core服务裁剪视频:")
            logger.info(f"  - 视频文件数量: {len(video_paths)}")
            logger.info(f"  - 裁剪模式: {clip_mode}")
            if clip_mode == "segments":
                logger.info(f"  - 段数: {segment_count}")
            else:
                logger.info(f"  - 每段时长: {segment_duration}秒")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 最大并发数: {max_concurrent}")

            # 调用gRPC方法，设置适当的超时时间（视频裁剪可能需要较长时间）
            response = self.stub.ClipVideos(request, timeout=1200.0)  # 20分钟超时

            # 构建裁剪结果
            results = []
            for result_item in response.results:
                segments = []
                for segment in result_item.segments:
                    segments.append({
                        "segment_index": segment.segment_index,
                        "start_time": segment.start_time,
                        "end_time": segment.end_time,
                        "duration": segment.duration,
                        "output_path": segment.output_path,
                        "file_size": segment.file_size,
                        "start_volume": segment.start_volume,
                        "end_volume": segment.end_volume
                    })

                results.append({
                    "input_path": result_item.input_path,
                    "success": result_item.success,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "segments": segments,
                    "original_duration": result_item.original_duration,
                    "volume_analysis_data": result_item.volume_analysis_data
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "results": results,
                "total_processing_time_ms": response.total_processing_time_ms,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count,
                "total_count": response.total_count,
                "output_folder": response.output_folder,
                "total_output_size": response.total_output_size,
                "total_segments": response.total_segments
            }

            if response.success:
                logger.info(f"视频裁剪成功，处理了 {response.successful_count} 个视频，生成 {response.total_segments} 个片段")
            else:
                logger.error(f"视频裁剪失败: {response.error}")

            return result

        except Exception as e:
            logger.error(f"裁剪视频失败: {str(e)}", exc_info=True)
            raise Exception(f"裁剪视频失败: {str(e)}") from e

    async def generate_subtitles_batch(self, folder_path: str, output_format: str = 'srt',
                                     language: str = 'auto', model_size: str = 'base',
                                     max_concurrent: int = 2) -> Dict[str, Any]:
        """批量生成字幕

        Args:
            folder_path: 文件夹路径
            output_format: 输出格式 (srt, vtt, txt)
            language: 语言代码 (auto, zh, en, etc.)
            model_size: Whisper模型大小 (tiny, base, small, medium, large)
            max_concurrent: 最大并发处理数

        Returns:
            生成结果
        """
        try:
            # 创建请求
            request = file_pb2.GenerateSubtitlesBatchRequest(
                folder_path=folder_path,
                output_format=output_format,
                language=language,
                model_size=model_size,
                max_concurrent=max_concurrent
            )

            logger.info(f"调用Core服务批量生成字幕:")
            logger.info(f"  - 文件夹路径: {folder_path}")
            logger.info(f"  - 输出格式: {output_format}")
            logger.info(f"  - 语言: {language}")
            logger.info(f"  - 模型大小: {model_size}")
            logger.info(f"  - 最大并发: {max_concurrent}")

            # 调用gRPC方法，设置适当的超时时间（字幕生成可能需要较长时间）
            response = self.stub.GenerateSubtitlesBatch(request, timeout=600.0)  # 10分钟超时

            # 构建结果
            results = []
            for result_item in response.results:
                results.append({
                    "success": result_item.success,
                    "input_file": result_item.input_file,
                    "output_file": result_item.output_file,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "input_file_size": result_item.input_file_size,
                    "output_file_size": result_item.output_file_size,
                    "message": result_item.message
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "processed_files": response.processed_files,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count,
                "output_directory": response.output_directory,
                "results": results
            }

            if response.success:
                logger.info(f"批量生成字幕完成: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量生成字幕失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"批量生成字幕失败: {e.details()}", exc_info=True)
            raise Exception(f"批量生成字幕失败: {e.details()}") from e

    async def extract_audio_batch(self, folder_path: str, output_format: str = 'wav',
                                quality: str = 'high', max_concurrent: int = 3) -> Dict[str, Any]:
        """批量分离音频

        Args:
            folder_path: 文件夹路径
            output_format: 输出音频格式 (wav, mp3, aac)
            quality: 音频质量 (high, medium, low)
            max_concurrent: 最大并发处理数

        Returns:
            分离结果
        """
        try:
            # 创建请求
            request = file_pb2.ExtractAudioBatchRequest(
                folder_path=folder_path,
                output_format=output_format,
                quality=quality,
                max_concurrent=max_concurrent
            )

            logger.info(f"调用Core服务批量分离音频:")
            logger.info(f"  - 文件夹路径: {folder_path}")
            logger.info(f"  - 输出格式: {output_format}")
            logger.info(f"  - 音频质量: {quality}")
            logger.info(f"  - 最大并发: {max_concurrent}")

            # 调用gRPC方法，设置适当的超时时间
            response = self.stub.ExtractAudioBatch(request, timeout=300.0)  # 5分钟超时

            # 构建结果
            results = []
            for result_item in response.results:
                results.append({
                    "success": result_item.success,
                    "input_file": result_item.input_file,
                    "output_file": result_item.output_file,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "input_file_size": result_item.input_file_size,
                    "output_file_size": result_item.output_file_size,
                    "message": result_item.message
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "processed_files": response.processed_files,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count,
                "output_directory": response.output_directory,
                "results": results
            }

            if response.success:
                logger.info(f"批量分离音频完成: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量分离音频失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"批量分离音频失败: {e.details()}", exc_info=True)
            raise Exception(f"批量分离音频失败: {e.details()}") from e

    async def separate_vocals_batch(self, folder_path: str, separation_method: str = 'ffmpeg',
                                  output_quality: str = 'high', max_concurrent: int = 2) -> Dict[str, Any]:
        """批量人声分离

        Args:
            folder_path: 文件夹路径
            separation_method: 分离方法 (ffmpeg, librosa)
            output_quality: 输出质量 (high, medium, low)
            max_concurrent: 最大并发处理数

        Returns:
            分离结果
        """
        try:
            # 创建请求
            request = file_pb2.SeparateVocalsBatchRequest(
                folder_path=folder_path,
                separation_method=separation_method,
                output_quality=output_quality,
                max_concurrent=max_concurrent
            )

            logger.info(f"调用Core服务批量人声分离:")
            logger.info(f"  - 文件夹路径: {folder_path}")
            logger.info(f"  - 分离方法: {separation_method}")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 最大并发: {max_concurrent}")

            # 调用gRPC方法，设置适当的超时时间（人声分离可能需要较长时间）
            response = self.stub.SeparateVocalsBatch(request, timeout=600.0)  # 10分钟超时

            # 构建结果
            results = []
            for result_item in response.results:
                results.append({
                    "success": result_item.success,
                    "input_file": result_item.input_file,
                    "vocals_file": result_item.vocals_file,
                    "instrumental_file": result_item.instrumental_file,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "input_file_size": result_item.input_file_size,
                    "vocals_file_size": result_item.vocals_file_size,
                    "instrumental_file_size": result_item.instrumental_file_size,
                    "message": result_item.message
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "processed_files": response.processed_files,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count,
                "output_directory": response.output_directory,
                "results": results
            }

            if response.success:
                logger.info(f"批量人声分离完成: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量人声分离失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"批量人声分离失败: {e.details()}", exc_info=True)
            raise Exception(f"批量人声分离失败: {e.details()}") from e

    async def replace_audio_batch(self, folder_path: str, new_audio_path: str,
                                audio_volume: float = 1.0, fade_duration: float = 0.5,
                                max_concurrent: int = 3) -> Dict[str, Any]:
        """批量替换音频

        Args:
            folder_path: 视频文件夹路径
            new_audio_path: 新音频文件路径
            audio_volume: 音频音量 (0.0-2.0)
            fade_duration: 淡入淡出时长（秒）
            max_concurrent: 最大并发处理数

        Returns:
            替换结果
        """
        try:
            # 创建请求
            request = file_pb2.ReplaceAudioBatchRequest(
                folder_path=folder_path,
                new_audio_path=new_audio_path,
                audio_volume=audio_volume,
                fade_duration=fade_duration,
                max_concurrent=max_concurrent
            )

            logger.info(f"调用Core服务批量替换音频:")
            logger.info(f"  - 文件夹路径: {folder_path}")
            logger.info(f"  - 新音频文件: {new_audio_path}")
            logger.info(f"  - 音频音量: {audio_volume}")
            logger.info(f"  - 淡入淡出时长: {fade_duration}秒")
            logger.info(f"  - 最大并发: {max_concurrent}")

            # 调用gRPC方法，设置适当的超时时间
            response = self.stub.ReplaceAudioBatch(request, timeout=600.0)  # 10分钟超时

            # 构建结果
            results = []
            for result_item in response.results:
                results.append({
                    "success": result_item.success,
                    "input_file": result_item.input_file,
                    "output_file": result_item.output_file,
                    "error_message": result_item.error_message,
                    "processing_time_ms": result_item.processing_time_ms,
                    "input_file_size": result_item.input_file_size,
                    "output_file_size": result_item.output_file_size,
                    "message": result_item.message
                })

            # 返回结果
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "processed_files": response.processed_files,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count,
                "output_directory": response.output_directory,
                "new_audio_file": response.new_audio_file,
                "results": results
            }

            if response.success:
                logger.info(f"批量替换音频完成: 处理了 {response.processed_files} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量替换音频失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"批量替换音频失败: {e.details()}", exc_info=True)
            raise Exception(f"批量替换音频失败: {e.details()}") from e

    async def generate_video_thumbnail(self, video_path: str, thumbnail_path: str = None,
                                      timestamp: float = None, width: int = 320, height: int = 180,
                                      quality: int = 85, force_regenerate: bool = False) -> Dict[str, Any]:
        """生成视频缩略图

        Args:
            video_path: 视频文件路径
            thumbnail_path: 缩略图输出路径（可选）
            timestamp: 缩略图时间点（秒）
            width: 最大缩略图宽度（保持比例）
            height: 最大缩略图高度（保持比例）
            quality: 图片质量（1-100）
            force_regenerate: 是否强制重新生成

        Returns:
            生成结果
        """
        try:
            # 创建请求
            request = file_pb2.GenerateVideoThumbnailRequest(
                video_path=video_path,
                width=width,
                height=height,
                quality=quality,
                force_regenerate=force_regenerate
            )

            if thumbnail_path:
                request.thumbnail_path = thumbnail_path
            if timestamp is not None:
                request.timestamp = timestamp

            logger.info(f"调用Core服务生成视频缩略图: {video_path}")

            # 调用gRPC方法
            response = self.stub.GenerateVideoThumbnail(request, timeout=60.0)

            # 转换响应
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "thumbnail_path": response.thumbnail_path,
                "thumbnail_url": response.thumbnail_url,
                "thumbnail_size": response.thumbnail_size,
                "actual_width": getattr(response, 'actual_width', 0),
                "actual_height": getattr(response, 'actual_height', 0),
                "generation_time_ms": response.generation_time_ms,
                "from_cache": response.from_cache
            }

            if response.success:
                logger.info(f"视频缩略图生成成功: {response.thumbnail_path}")
            else:
                logger.error(f"视频缩略图生成失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"生成视频缩略图失败: {e.details()}", exc_info=True)
            raise Exception(f"生成视频缩略图失败: {e.details()}") from e

    async def get_video_preview_info(self, video_path: str, include_thumbnail: bool = True,
                                   include_detailed_metadata: bool = False) -> Dict[str, Any]:
        """获取视频预览信息

        Args:
            video_path: 视频文件路径
            include_thumbnail: 是否包含缩略图信息
            include_detailed_metadata: 是否包含详细元数据

        Returns:
            视频预览信息
        """
        try:
            # 创建请求
            request = file_pb2.GetVideoPreviewInfoRequest(
                video_path=video_path,
                include_thumbnail=include_thumbnail,
                include_detailed_metadata=include_detailed_metadata
            )

            logger.info(f"调用Core服务获取视频预览信息: {video_path}")

            # 调用gRPC方法
            response = self.stub.GetVideoPreviewInfo(request, timeout=60.0)

            # 转换响应
            result = {
                "success": response.success,
                "error": response.error if not response.success else ""
            }

            if response.success:
                # 转换媒体信息
                if response.HasField('media_info'):
                    media_info = response.media_info
                    result["media_info"] = {
                        "duration": media_info.duration,
                        "resolution": media_info.resolution,
                        "video_codec": media_info.video_codec,
                        "audio_codec": media_info.audio_codec,
                        "frame_rate": media_info.frame_rate,
                        "bitrate": media_info.bitrate
                    }

                # 转换缩略图信息
                if response.HasField('thumbnail_info'):
                    thumbnail_info = response.thumbnail_info
                    result["thumbnail_info"] = {
                        "thumbnail_path": thumbnail_info.thumbnail_path,
                        "thumbnail_size": thumbnail_info.thumbnail_size,
                        "width": thumbnail_info.width,
                        "height": thumbnail_info.height,
                        "exists": thumbnail_info.exists,
                        "generated_time": thumbnail_info.generated_time
                    }

                # 转换详细元数据
                if response.HasField('detailed_metadata'):
                    detailed_metadata = response.detailed_metadata
                    result["detailed_metadata"] = {
                        "format": detailed_metadata.format,
                        "creation_time": detailed_metadata.creation_time,
                        "title": detailed_metadata.title,
                        "author": detailed_metadata.author,
                        "description": detailed_metadata.description,
                        "tags": list(detailed_metadata.tags)
                    }

                logger.info(f"视频预览信息获取成功: {video_path}")
            else:
                logger.error(f"视频预览信息获取失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"获取视频预览信息失败: {e.details()}", exc_info=True)
            raise Exception(f"获取视频预览信息失败: {e.details()}") from e

    async def generate_video_preview_clip(self, video_path: str, preview_clip_path: str = None,
                                        start_time: float = 0, duration: float = 30,
                                        output_quality: str = "medium", force_regenerate: bool = False) -> Dict[str, Any]:
        """生成视频预览片段

        Args:
            video_path: 视频文件路径
            preview_clip_path: 预览片段输出路径（可选）
            start_time: 预览开始时间（秒）
            duration: 预览时长（秒）
            output_quality: 输出质量（high/medium/low）
            force_regenerate: 是否强制重新生成

        Returns:
            生成结果
        """
        try:
            # 创建请求
            request = file_pb2.GenerateVideoPreviewClipRequest(
                video_path=video_path,
                start_time=start_time,
                duration=duration,
                output_quality=output_quality,
                force_regenerate=force_regenerate
            )

            if preview_clip_path:
                request.preview_clip_path = preview_clip_path

            logger.info(f"调用Core服务生成视频预览片段: {video_path}")

            # 调用gRPC方法
            response = self.stub.GenerateVideoPreviewClip(request, timeout=120.0)

            # 转换响应
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "preview_clip_path": response.preview_clip_path,
                "preview_clip_size": response.preview_clip_size,
                "generation_time_ms": response.generation_time_ms,
                "from_cache": response.from_cache
            }

            if response.success:
                logger.info(f"视频预览片段生成成功: {response.preview_clip_path}")
            else:
                logger.error(f"视频预览片段生成失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"生成视频预览片段失败: {e.details()}", exc_info=True)
            raise Exception(f"生成视频预览片段失败: {e.details()}") from e

    async def batch_add_intro_outro(self, folder_path: str, selected_files: List[str] = None,
                                   intro_path: str = None, outro_path: str = None,
                                   output_folder: str = None, transition_effect: str = 'fade',
                                   transition_duration: float = 1.0, output_quality: str = 'medium',
                                   max_concurrent: int = 3, overwrite_original: bool = False,
                                   output_suffix: str = '_with_intro_outro') -> Dict[str, Any]:
        """批量添加视频片头片尾

        Args:
            folder_path: 视频文件夹路径
            selected_files: 选中的文件列表（可选）
            intro_path: 片头文件路径（可选）
            outro_path: 片尾文件路径（可选）
            output_folder: 输出文件夹路径（可选）
            transition_effect: 转场效果
            transition_duration: 转场时长
            output_quality: 输出质量
            max_concurrent: 最大并发数
            overwrite_original: 是否覆盖原文件
            output_suffix: 输出文件后缀

        Returns:
            处理结果
        """
        try:
            # 创建请求
            request = file_pb2.BatchAddIntroOutroRequest(
                folder_path=folder_path,
                intro_path=intro_path or "",
                outro_path=outro_path or "",
                output_folder=output_folder or "",
                transition_effect=transition_effect,
                transition_duration=transition_duration,
                output_quality=output_quality,
                max_concurrent=max_concurrent,
                overwrite_original=overwrite_original,
                output_suffix=output_suffix
            )

            # 添加选中文件列表（如果支持的话）
            if selected_files and len(selected_files) > 0:
                try:
                    # 检查 request 对象是否有 selected_files 属性
                    if hasattr(request, 'selected_files'):
                        for file_name in selected_files:
                            request.selected_files.append(file_name)
                        logger.info(f"成功添加 {len(selected_files)} 个选中文件到 gRPC 请求")
                    else:
                        logger.warning("gRPC 请求对象没有 selected_files 属性")
                except AttributeError as e:
                    # 如果 gRPC 代码还没有更新，暂时忽略 selected_files
                    logger.warning(f"gRPC 代码尚未支持 selected_files 字段: {e}")
                    pass
            else:
                logger.info("没有选中文件或选中文件列表为空")

            logger.info(f"调用Core服务批量添加片头片尾: {folder_path}")
            logger.info(f"selected_files 参数: {selected_files}")
            logger.info(f"request 对象类型: {type(request)}")
            if selected_files:
                logger.info(f"选中文件: {selected_files}")
                logger.info(f"选中文件数量: {len(selected_files)}")
            else:
                logger.info("没有选中文件，将处理整个文件夹")

            # 调用gRPC方法，设置较长的超时时间（视频处理可能需要很长时间）
            response = self.stub.BatchAddIntroOutro(request, timeout=1800.0)  # 30分钟超时

            # 解析响应
            result = {
                "success": response.success,
                "error": response.error if not response.success else "",
                "total_count": response.total_count,
                "successful_count": response.successful_count,
                "failed_count": response.failed_count,
                "total_processing_time_ms": response.total_processing_time_ms,
                "output_folder": response.output_folder,
                "total_output_size": response.total_output_size,
                "results": []
            }

            # 解析处理结果
            for item in response.results:
                result["results"].append({
                    "input_path": item.input_path,
                    "output_path": item.output_path,
                    "success": item.success,
                    "error_message": item.error_message,
                    "processing_time_ms": item.processing_time_ms,
                    "output_size": item.output_size
                })

            if response.success:
                logger.info(f"批量添加片头片尾成功: 处理了 {response.total_count} 个文件，成功 {response.successful_count} 个")
            else:
                logger.error(f"批量添加片头片尾失败: {response.error}")

            return result
        except grpc.RpcError as e:
            logger.error(f"批量添加片头片尾失败: {e.details()}", exc_info=True)
            raise Exception(f"批量添加片头片尾失败: {e.details()}") from e

    async def close(self):
        """关闭连接"""
        if self.channel:
            await self.channel.close()
            logger.info("已关闭Core文件服务连接")


class TaskServiceClient:
    """任务服务gRPC客户端"""

    def __init__(self, host: str = 'localhost', port: int = 50051):
        """初始化任务服务客户端

        Args:
            host: Core服务主机
            port: Core服务端口
        """
        self.host = host
        self.port = port
        self.channel = None
        self.stub = None
        self._connect()

    def _connect(self):
        """连接到gRPC服务"""
        try:
            # 创建gRPC通道，设置连接选项
            options = [
                ('grpc.keepalive_time_ms', 120000),  # keepalive时间从60秒增加到120秒
                ('grpc.keepalive_timeout_ms', 30000),  # keepalive超时从10秒增加到30秒
                ('grpc.keepalive_permit_without_calls', False),  # 不允许无调用时发送keepalive
                ('grpc.http2.max_pings_without_data', 2),  # 限制无数据时的ping数量
                ('grpc.http2.min_time_between_pings_ms', 60000),  # ping间隔从30秒增加到60秒
                ('grpc.http2.min_ping_interval_without_data_ms', 600000),  # 无数据时ping间隔从5分钟增加到10分钟
                ('grpc.max_connection_idle_ms', 600000),  # 连接空闲时间从5分钟增加到10分钟
                ('grpc.max_connection_age_ms', 1200000),  # 连接最大存活从10分钟增加到20分钟
                ('grpc.max_connection_age_grace_ms', 60000)  # 连接关闭宽限期从30秒增加到60秒
            ]
            self.channel = grpc.insecure_channel(f"{self.host}:{self.port}", options=options)
            # 创建gRPC存根
            self.stub = task_pb2_grpc.TaskServiceStub(self.channel)
            logger.info(f"已连接到Core任务服务: {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"连接Core任务服务失败: {str(e)}", exc_info=True)
            raise Exception(f"连接Core任务服务失败: {str(e)}") from e

    async def create_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建任务

        Args:
            task_data: 任务数据

        Returns:
            创建结果
        """
        try:
            # 创建请求
            request = task_pb2.TaskRequest(
                task_id=task_data.get("task_id", ""),
                task_type=task_data.get("task_type", "single"),  # 🔧 重要修复：添加任务类型字段
                platform_id=task_data.get("platform_id", ""),
                account_id=task_data.get("account_id", ""),
                device_id=task_data.get("device_id", ""),
                content_path=task_data.get("content_path", "")
            )

            # 添加可选字段
            if "workflow_id" in task_data and task_data["workflow_id"]:
                try:
                    request.workflow_id = str(task_data["workflow_id"])
                except Exception as e:
                    logger.warning(f"设置workflow_id失败: {str(e)}")

            # 🔧 重要修复：为下载任务添加特有字段到params
            if task_data.get("task_type") in ["benchmark_download", "benchmark_download_batch"]:
                download_fields = [
                    "platform", "benchmark_account_url", "benchmark_account_name",
                    "download_path", "our_account_id", "our_account_name",
                    "benchmark_account_id"
                ]
                for field in download_fields:
                    if field in task_data and task_data[field]:
                        request.params[field] = str(task_data[field])
                        logger.info(f"添加下载任务字段到params: {field} = {task_data[field]}")

                # 特殊处理download_config（序列化为JSON）
                if "download_config" in task_data and task_data["download_config"]:
                    import json
                    request.params["download_config"] = json.dumps(task_data["download_config"], ensure_ascii=False)
                    logger.info(f"添加下载配置到params: download_config")

            # 添加任务参数
            if "params" in task_data and isinstance(task_data["params"], dict):
                for key, value in task_data["params"].items():
                    request.params[key] = str(value)

            # 添加任务元数据（作为params传递）
            if "metadata" in task_data and isinstance(task_data["metadata"], dict):
                logger.info(f"=== 传递任务元数据到Core服务 ===")
                logger.info(f"完整metadata: {task_data['metadata']}")

                # 特别检查音乐配置
                if 'selectedMusic' in task_data["metadata"]:
                    selected_music = task_data["metadata"]['selectedMusic']
                    logger.info(f"✅ 检测到selectedMusic: {selected_music}")
                    logger.info(f"selectedMusic类型: {type(selected_music)}")
                    logger.info(f"selectedMusic长度: {len(selected_music) if isinstance(selected_music, list) else 'Not a list'}")
                else:
                    logger.warning("❌ metadata中未检测到selectedMusic字段")

                for key, value in task_data["metadata"].items():
                    # 使用metadata_前缀来区分元数据和普通参数
                    param_key = f"metadata_{key}"

                    # 对于复杂对象（如selectedMusic），需要序列化为JSON字符串
                    if isinstance(value, (list, dict)):
                        import json
                        value_str = json.dumps(value, ensure_ascii=False)
                        logger.info(f"序列化复杂对象 {param_key}: {value_str}")
                    else:
                        value_str = str(value)

                    request.params[param_key] = value_str
                    logger.debug(f"添加元数据参数: {param_key} = {value_str}")

            # 调用gRPC方法，设置240秒超时（任务创建可能需要较长时间）
            response = self.stub.CreateTask(request, timeout=240.0)  # 超时时间从120秒增加到240秒

            # 返回结果
            return {
                "success": response.success,
                "task_id": response.task_id,
                "error": response.error if not response.success else ""
            }
        except grpc.RpcError as e:
            logger.error(f"创建任务失败: {e.details()}", exc_info=True)
            raise Exception(f"创建任务失败: {e.details()}") from e

    async def start_task(self, task_id: str) -> Dict[str, Any]:
        """开始执行任务

        Args:
            task_id: 任务ID

        Returns:
            执行结果
        """
        try:
            logger.info(f"🚀 Backend发送启动任务请求: {task_id}")
            logger.info(f"📋 task_id类型: {type(task_id)}, 长度: {len(task_id) if task_id else 'None'}")

            # 创建请求
            request = task_pb2.TaskIdRequest(task_id=task_id)
            logger.info(f"📋 创建的gRPC请求: task_id='{request.task_id}', 类型: {type(request.task_id)}")

            # 调用gRPC方法，设置60秒超时（任务启动通常较快）
            logger.info(f"📡 发送gRPC StartTask请求: {task_id}")
            response = self.stub.StartTask(request, timeout=60.0)
            logger.info(f"📋 收到gRPC StartTask响应: success={response.success}, task_id='{response.task_id}', error='{response.error}'")

            # 返回结果
            result = {
                "success": response.success,
                "task_id": response.task_id,
                "error": response.error if not response.success else ""
            }
            logger.info(f"📋 Backend启动任务结果: {result}")
            return result
        except grpc.RpcError as e:
            logger.error(f"❌ Backend启动任务gRPC失败: {e.details()}", exc_info=True)
            raise Exception(f"开始执行任务失败: {e.details()}") from e

    async def pause_task(self, task_id: str) -> Dict[str, Any]:
        """暂停任务

        Args:
            task_id: 任务ID

        Returns:
            暂停结果
        """
        try:
            # 创建请求
            request = task_pb2.TaskIdRequest(task_id=task_id)

            # 调用gRPC方法
            response = self.stub.PauseTask(request)

            # 返回结果
            return {
                "success": response.success,
                "task_id": response.task_id,
                "error": response.error if not response.success else ""
            }
        except grpc.RpcError as e:
            logger.error(f"暂停任务失败: {e.details()}", exc_info=True)
            raise Exception(f"暂停任务失败: {e.details()}") from e

    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务

        Args:
            task_id: 任务ID

        Returns:
            取消结果
        """
        try:
            # 创建请求
            request = task_pb2.TaskIdRequest(task_id=task_id)

            # 调用gRPC方法
            response = self.stub.CancelTask(request)

            # 返回结果
            return {
                "success": response.success,
                "task_id": response.task_id,
                "error": response.error if not response.success else ""
            }
        except grpc.RpcError as e:
            logger.error(f"取消任务失败: {e.details()}", exc_info=True)
            raise Exception(f"取消任务失败: {e.details()}") from e

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态
        """
        try:
            # 创建请求
            request = task_pb2.TaskIdRequest(task_id=task_id)

            # 调用gRPC方法
            response = self.stub.GetTaskStatus(request)

            # 构建设备使用情况
            device_usage = {
                "cpu": response.device_usage.cpu,
                "memory": response.device_usage.memory,
                "network": response.device_usage.network
            }

            # 构建日志
            logs = []
            for log in response.logs:
                logs.append({
                    "message": log.message,
                    "level": log.level,
                    "timestamp": log.timestamp
                })

            # 返回结果
            return {
                "task_id": response.task_id,
                "status": response.status,
                "progress": response.progress,
                "start_time": response.start_time,
                "estimated_end_time": response.estimated_end_time,
                "device_usage": device_usage,
                "logs": logs
            }
        except grpc.RpcError as e:
            logger.error(f"获取任务状态失败: {e.details()}", exc_info=True)
            raise Exception(f"获取任务状态失败: {e.details()}") from e

    async def get_task_logs(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务日志

        Args:
            task_id: 任务ID

        Returns:
            任务日志
        """
        try:
            # 创建请求
            request = task_pb2.TaskIdRequest(task_id=task_id)

            # 调用gRPC方法
            response = self.stub.GetTaskLogs(request)

            # 构建日志
            logs = []
            for log in response.logs:
                logs.append({
                    "message": log.message,
                    "level": log.level,
                    "timestamp": log.timestamp
                })

            return logs
        except grpc.RpcError as e:
            logger.error(f"获取任务日志失败: {e.details()}", exc_info=True)
            raise Exception(f"获取任务日志失败: {e.details()}") from e

    async def get_workflow_config(self, platform_id: str, content_type: str) -> Dict[str, Any]:
        """获取工作流配置

        Args:
            platform_id: 平台ID
            content_type: 内容类型

        Returns:
            工作流配置
        """
        try:
            # 创建请求
            request = task_pb2.WorkflowConfigRequest(
                platform_id=platform_id,
                content_type=content_type
            )

            # 调用gRPC方法
            response = self.stub.GetWorkflowConfig(request)

            # 构建步骤列表
            steps = []
            for step in response.steps:
                steps.append({
                    "id": step.id,
                    "name": step.name,
                    "description": step.description,
                    "action": step.action,
                    "required": step.required,
                    "timeout": step.timeout,
                    "retry_count": step.retry_count,
                    "wait_after": step.wait_after,
                    "condition": step.condition,
                    "element": step.element,
                    "parameters": step.parameters,
                    "notes": step.notes
                })

            # 返回结果
            return {
                "success": response.success,
                "error": response.error,
                "workflow_id": response.workflow_id,
                "workflow_name": response.workflow_name,
                "workflow_description": response.workflow_description,
                "workflow_version": response.workflow_version,
                "steps": steps,
                "config": response.config
            }
        except grpc.RpcError as e:
            logger.error(f"获取工作流配置失败: {e.details()}", exc_info=True)
            return {
                "success": False,
                "error": f"获取工作流配置失败: {e.details()}",
                "steps": []
            }

    async def close(self):
        """关闭连接"""
        if self.channel:
            await self.channel.close()
            logger.info("已关闭Core任务服务连接")


class CoreClient:
    """Core服务统一客户端（兼容旧代码）"""

    def __init__(self, host: str = 'localhost', port: int = 50051):
        """初始化Core客户端

        Args:
            host: Core服务主机
            port: Core服务端口
        """
        self.host = host
        self.port = port
        self.task_client = TaskServiceClient(host, port)
        self.device_client = DeviceServiceClient(host, port)
        self.file_client = FileServiceClient(host, port)
        logger.info(f"已初始化Core客户端: {host}:{port}")

    async def create_task(self, task_id: str, task_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建任务（兼容旧接口）

        Args:
            task_id: 任务ID
            task_data: 任务数据（可选）

        Returns:
            创建结果
        """
        try:
            logger.info(f"📝 CoreClient创建任务(兼容接口): {task_id}")

            # 如果没有提供task_data，使用默认数据
            if task_data is None:
                task_data = {
                    "task_id": task_id,
                    "platform_id": "youtube",
                    "account_id": "default",
                    "device_id": "85",
                    "content_path": "/default/path"
                }
                logger.info(f"📋 使用默认任务数据")
            else:
                logger.info(f"📋 使用提供的任务数据，字段数: {len(task_data)}")

            # 确保task_id在task_data中
            task_data["task_id"] = task_id

            # 确保task_type字段存在
            if "task_type" not in task_data:
                task_data["task_type"] = "single"
                logger.info(f"📋 添加默认task_type: single")

            logger.info(f"🔄 调用TaskServiceClient创建任务: {task_id}")
            result = await self.task_client.create_task(task_data)
            logger.info(f"📋 TaskServiceClient创建结果: {result}")
            return result
        except Exception as e:
            logger.error(f"❌ CoreClient创建任务失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def start_task(self, task_id: str) -> Dict[str, Any]:
        """启动任务

        Args:
            task_id: 任务ID

        Returns:
            启动结果
        """
        try:
            logger.info(f"CoreClient启动任务: {task_id}")
            return await self.task_client.start_task(task_id)
        except Exception as e:
            logger.error(f"CoreClient启动任务失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def pause_task(self, task_id: str) -> Dict[str, Any]:
        """暂停任务

        Args:
            task_id: 任务ID

        Returns:
            暂停结果
        """
        try:
            logger.info(f"CoreClient暂停任务: {task_id}")
            return await self.task_client.pause_task(task_id)
        except Exception as e:
            logger.error(f"CoreClient暂停任务失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务

        Args:
            task_id: 任务ID

        Returns:
            取消结果
        """
        try:
            logger.info(f"CoreClient取消任务: {task_id}")
            return await self.task_client.cancel_task(task_id)
        except Exception as e:
            logger.error(f"CoreClient取消任务失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态
        """
        try:
            logger.info(f"CoreClient获取任务状态: {task_id}")
            return await self.task_client.get_task_status(task_id)
        except Exception as e:
            logger.error(f"CoreClient获取任务状态失败: {str(e)}")
            return {"task_id": task_id, "status": "unknown", "error": str(e)}

    # 调度任务相关方法 - 暂时返回模拟数据，等待gRPC接口实现
    async def create_scheduled_task(self, task_config: Dict[str, Any]) -> str:
        """创建调度任务"""
        try:
            # TODO: 实现gRPC调用
            logger.warning("调度任务功能暂未实现gRPC接口，返回模拟数据")
            import time
            import hashlib
            task_id = f"scheduled_{int(time.time())}_{hashlib.md5(str(task_config).encode()).hexdigest()[:8]}"
            return task_id

        except Exception as e:
            logger.error(f"创建调度任务失败: {str(e)}", exc_info=True)
            raise

    async def pause_scheduled_task(self, task_id: str) -> bool:
        """暂停调度任务"""
        try:
            # TODO: 实现gRPC调用
            logger.warning("调度任务功能暂未实现gRPC接口，返回模拟数据")
            return True

        except Exception as e:
            logger.error(f"暂停调度任务失败: {str(e)}", exc_info=True)
            return False

    async def resume_scheduled_task(self, task_id: str) -> bool:
        """恢复调度任务"""
        try:
            # TODO: 实现gRPC调用
            logger.warning("调度任务功能暂未实现gRPC接口，返回模拟数据")
            return True

        except Exception as e:
            logger.error(f"恢复调度任务失败: {str(e)}", exc_info=True)
            return False

    async def delete_scheduled_task(self, task_id: str) -> bool:
        """删除调度任务"""
        try:
            # TODO: 实现gRPC调用
            logger.warning("调度任务功能暂未实现gRPC接口，返回模拟数据")
            return True

        except Exception as e:
            logger.error(f"删除调度任务失败: {str(e)}", exc_info=True)
            return False

    async def delete_files(self, file_paths: List[str]) -> Dict[str, Any]:
        """删除文件

        Args:
            file_paths: 要删除的文件路径列表

        Returns:
            删除结果
        """
        try:
            logger.info(f"CoreClient删除文件，数量: {len(file_paths)}")
            return await self.file_client.delete_files(file_paths)
        except Exception as e:
            logger.error(f"CoreClient删除文件失败: {str(e)}")
            return {
                "success": False,
                "deleted_count": 0,
                "total_count": len(file_paths),
                "errors": [str(e)]
            }

    async def get_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """获取调度任务列表"""
        try:
            # TODO: 实现gRPC调用
            logger.warning("调度任务功能暂未实现gRPC接口，返回模拟数据")
            return []

        except Exception as e:
            logger.error(f"获取调度任务列表失败: {str(e)}", exc_info=True)
            return []

    async def close(self):
        """关闭连接"""
        try:
            await self.task_client.close()
            await self.device_client.close()
            logger.info("已关闭CoreClient连接")
        except Exception as e:
            logger.error(f"关闭CoreClient连接失败: {str(e)}")
