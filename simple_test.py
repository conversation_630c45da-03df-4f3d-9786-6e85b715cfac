#!/usr/bin/env python3
"""
简单测试工作流API
"""

import requests
import json

def test_workflow_api():
    """测试工作流API"""
    try:
        # 测试一个不存在的任务
        url = "http://localhost:8000/api/v1/workflow/test_task_123/workflow"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    test_workflow_api()
