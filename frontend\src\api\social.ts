import request from '@/utils/request'
import type { SocialAccount, SocialApp, SocialPost, SocialPlatform, PlatformApp } from '@/types/social'

// 获取社媒应用列表
export const getSocialApps = () => {
  return request<SocialApp[]>({
    url: '/api/social/apps',
    method: 'get'
  })
}

// 获取平台列表
export const getPlatforms = (status?: string) => {
  const params = status ? { status } : {}
  return request<SocialPlatform[]>({
    url: '/api/v1/social/platforms',
    method: 'get',
    params
  })
}

// 获取单个平台
export const getPlatform = (id: string) => {
  return request<SocialPlatform>({
    url: `/api/v1/social/platforms/${id}`,
    method: 'get'
  })
}

// 创建平台
export const createPlatform = (data: Omit<SocialPlatform, '_id'>) => {
  return request<SocialPlatform>({
    url: '/api/v1/social/platforms',
    method: 'post',
    data
  })
}

// 更新平台
export const updatePlatform = (id: string, data: Partial<SocialPlatform>) => {
  return request<SocialPlatform>({
    url: `/api/v1/social/platforms/${id}`,
    method: 'put',
    data
  })
}

// 删除平台
export const deletePlatform = (id: string) => {
  return request<{ deleted: boolean }>({
    url: `/api/v1/social/platforms/${id}`,
    method: 'delete'
  })
}

// 获取平台应用列表
export const getPlatformApps = (platformId: string, type?: string) => {
  const params = type ? { type } : {}
  return request<PlatformApp[]>({
    url: `/api/v1/social/platforms/${platformId}/apps`,
    method: 'get',
    params
  })
}

// 获取账号列表（通用）- 后端返回格式
export interface AccountsBackendResponse {
  data: SocialAccount[]
  total: number
  page: number
  page_size: number
}

// 经过响应拦截器处理后的格式
export interface AccountsResponse {
  success: true
  data: AccountsBackendResponse
}

export const getAccounts = (params?: {
  platform_id?: string
  core_service_id?: string
  status?: string
  keyword?: string
  skip?: number
  limit?: number
  app_id?: string
}) => {
  return request<AccountsResponse>({
    url: '/api/v1/social/accounts',
    method: 'get',
    params
  })
}

// 添加账号
export const addAccount = (data: Omit<SocialAccount, 'id'>) => {
  return request<SocialAccount>({
    url: '/api/v1/social/accounts',
    method: 'post',
    data
  })
}

// 创建账号（新API）
export const createAccount = (data: Omit<SocialAccount, 'id'>) => {
  return request<SocialAccount>({
    url: '/api/v1/social/accounts',
    method: 'post',
    data
  })
}

// 更新单个账号
export const updateAccount = (id: string, data: Partial<SocialAccount>) => {
  return request<SocialAccount>({
    url: `/api/v1/social/accounts/${id}`,
    method: 'put',
    data
  })
}

// 发布内容
export const createPost = (data: Omit<SocialPost, 'id'>) => {
  return request<SocialPost>({
    url: '/api/social/posts',
    method: 'post',
    data
  })
}

export interface SocialAnalyticsItem {
  _id?: {
    platform?: string
    date?: string
  }
  likes?: number
  comments?: number
  shares?: number
  views?: number
  [key: string]: any
}

export interface SocialAnalyticsResponse {
  data: SocialAnalyticsItem[]
  summary: {
    total_likes: number
    total_comments: number
    total_shares: number
    total_views: number
  }
}

// 获取分析数据
export const getAnalytics = (params: {
  appId?: string
  accountId?: string
  days?: number
  platform?: string
}) => {
  return request<SocialAnalyticsResponse>({
    url: '/api/social/analytics',
    method: 'get',
    params
  })
}

// 获取内容列表
export const getPosts = (appId: string) => {
  return request<SocialPost[]>({
    url: `/api/social/posts?app_id=${appId}`,
    method: 'get'
  })
}

// 控制社媒应用
export const controlApp = (data: {
  deviceId: string
  appId: string
  action: 'start' | 'stop' | 'clear'
}) => {
  return request({
    url: '/api/social/control',
    method: 'post',
    data
  })
}

// 复制账号
export const copyAccount = (id: string) => {
  return request<SocialAccount>({
    url: `/api/v1/social/accounts/${id}/copy`,
    method: 'post'
  })
}

// 删除单个账号
export const deleteAccount = (id: string) => {
  return request<{ deleted: boolean }>({
    url: `/api/v1/social/accounts/${id}`,
    method: 'delete'
  })
}

// 批量删除账号
export const batchDeleteAccounts = (accountIds: string[]) => {
  return request<{ deleted_count: number }>({
    url: '/api/v1/social/accounts/batch_delete',
    method: 'post',
    data: {
      account_ids: accountIds
    }
  })
}

// 批量更新账号
export const batchUpdateAccounts = (accountIds: string[], updateData: Partial<SocialAccount>) => {
  return request<{ updated_count: number }>({
    url: '/api/v1/social/accounts/batch_update',
    method: 'post',
    data: {
      account_ids: accountIds,
      update_data: updateData
    }
  })
}

// 导入账号
export interface ImportAccountsResponse {
  imported_count: number;
  errors: string[];
  [key: string]: any;
}

export const importAccounts = (
  textContent: string,
  platformMapping?: Record<string, string>,
  coreServiceId?: string,
  format: 'text' | 'csv' = 'text'
) => {
  return request<ImportAccountsResponse>({
    url: '/api/v1/social/accounts/import',
    method: 'post',
    data: {
      text_content: textContent,
      platform_mapping: platformMapping,
      core_service_id: coreServiceId,
      format: format
    }
  })
}

// 导出账号
export const exportAccounts = (params?: {
  platform_id?: string
  core_service_id?: string
  format?: 'csv' | 'json'
  account_ids?: string[] // 添加账号ID列表参数
}) => {
  // 如果有账号ID列表，使用POST方法，否则使用GET方法
  if (params?.account_ids && params.account_ids.length > 0) {
    return request({
      url: '/api/v1/social/accounts/export',
      method: 'post',
      data: {
        account_ids: params.account_ids,
        format: params.format || 'csv'
      },
      responseType: params?.format === 'json' ? 'json' : 'blob'
    })
  } else {
    return request({
      url: '/api/v1/social/accounts/export',
      method: 'get',
      params,
      responseType: params?.format === 'json' ? 'json' : 'blob'
    })
  }
}

export interface PublishTask {
  platform_id: string
  account_id: string
  content_path: string
  schedule_type?: 'immediate' | 'scheduled'
  schedule_time?: string
  workflow_id?: string // 添加工作流ID字段
  selected_files?: string[] // 添加选中文件列表
}

export interface Workflow {
  id: string
  name: string
  description: string
  created_at: string
}

// 修改工作流API路径，使用正确的API版本
export const getWorkflows = () => {
  return request<Workflow[]>({
    // 修改为可能存在的API路径
    url: '/api/social/workflows', // 从v1改为social命名空间
    method: 'get'
  })
}

export const createTask = (data: PublishTask, coreServiceId?: string) => {
  // 🔧 重要修复：添加core_service_id到任务数据中
  const taskData = { ...data }
  if (coreServiceId) {
    taskData.core_service_id = coreServiceId
  }

  return request({
    url: '/api/social/tasks',
    method: 'post',
    data: taskData
  })
}

export interface ValidateFolderResponse {
  valid: boolean
  files: Array<{
    name: string
    size: number
    type: string
    valid: boolean
  }>
}

export const validateFolder = (data: { path: string }) => {
  return request({
    url: '/api/v1/social/validate-folder',
    method: 'post',
    data
  })
}

// 获取特定平台账号列表（重命名以避免冲突）
export const getPlatformAccounts = (platform: string) => {
  return request({
    url: `/api/v1/social/${platform}/accounts`,
    method: 'get'
  })
}

// 创建上传任务
export const createUploadTask = (data: {
  folderPath: string
  accountId: string
  selectedFiles?: string[]  // 添加选中文件列表
  metadata: {
    titleTemplate: string
    description: string
    tags: string[]
    privacyStatus: string
    contentType: string  // 新增内容类型字段
    musicLibrary?: string
    publishStrategy?: string
  }
}) => {
  return request({
    url: '/api/v1/social/youtube/uploads',
    method: 'post',
    data
  })
}

// 获取上传任务列表
export const getUploadTasks = () => {
  return request({
    url: '/api/v1/social/youtube/uploads',
    method: 'get'
  })
}

// 获取任务日志
export const getTaskLogs = (taskId: string) => {
  return request({
    url: `/api/tasks/detail/${taskId}/logs`,
    method: 'get'
  })
}

// 获取任务结果
export const getTaskResult = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/result`,
    method: 'get'
  })
}

// 获取任务列表
export const getTaskList = (params?: {
  status?: string
  platform_id?: string
  limit?: number
  offset?: number
}) => {
  return request({
    url: '/api/tasks',
    method: 'get',
    params
  })
}

// 获取任务状态
export const getTaskStatus = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/status`,
    method: 'get'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'unknown', message: '无法获取任务状态' }
  }).catch(error => {
    console.error('获取任务状态失败:', error)
    throw error
  })
}

// 更新任务配置
export const updateTaskConfig = (taskId: string, config: any) => {
  return request({
    url: `/api/social/tasks/${taskId}/config`,
    method: 'put',
    data: config
  })
}

// 开始执行任务
export const startTaskExecution = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/start`,
    method: 'post'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'running', message: '任务已开始执行' }
  }).catch(error => {
    console.error('开始任务执行失败:', error)
    throw error
  })
}

// 暂停任务
export const pauseTaskExecution = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/pause`,
    method: 'post'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'paused', message: '任务已暂停' }
  }).catch(error => {
    console.error('暂停任务失败:', error)
    throw error
  })
}

// 取消任务
export const cancelTaskExecution = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/cancel`,
    method: 'post'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'canceled', message: '任务已取消' }
  }).catch(error => {
    console.error('取消任务失败:', error)
    throw error
  })
}

// 修复任务的Core服务配置
export const fixTaskCoreService = (taskId: string, coreServiceId: string) => {
  return request({
    url: `/api/tasks/${taskId}/fix-core-service`,
    method: 'post',
    data: {
      core_service_id: coreServiceId
    }
  }).then(response => {
    return response.data || { success: false, error: '修复失败' }
  }).catch(error => {
    console.error('修复任务Core服务配置失败:', error)
    throw error
  })
}

// 文件系统API
export interface FileInfo {
  name: string
  path: string
  size: number
  is_directory: boolean
  extension?: string
  last_modified?: string
}

export interface FolderListResponse {
  path: string
  files: FileInfo[]
  parent_path?: string
}

// 获取文件夹内容（增强版）
export const listFolderContents = (
  path: string,
  filterExtensions?: string[],
  includeMd5?: boolean,
  includeMediaInfo?: boolean,
  coreServiceId?: string
) => {
  const params: any = {}
  if (filterExtensions) params.filter_extensions = filterExtensions
  if (includeMd5) params.include_md5 = includeMd5
  if (includeMediaInfo) params.include_media_info = includeMediaInfo
  if (coreServiceId !== undefined) params.core_service_id = coreServiceId

  return request<FolderListResponse>({
    url: '/api/v1/filesystem/list',
    method: 'post',
    data: { path },
    params
  })
}

// 获取驱动器列表
export const listDrives = () => {
  return request<string[]>({
    url: '/api/v1/filesystem/drives',
    method: 'get'
  })
}

// 验证视频文件夹
export const validateVideoFolder = (path: string) => {
  return request({
    url: '/api/v1/filesystem/validate-video-folder',
    method: 'post',
    data: { path }
  })
}

// 获取Core服务列表
export const getCoreServices = () => {
  return request({
    url: '/api/v1/cores',
    method: 'get'
  })
}

// 批量重命名文件
export interface BatchRenameRequest {
  folder_path: string
  file_names: string[]
  selected_files?: string[]  // 可选：指定要重命名的文件列表
}

export interface BatchRenameResponse {
  success: boolean
  renamed_count: number
  errors: string[]
}

// 归档已发布文件
export interface ArchivePublishedFilesRequest {
  folder_path: string
  archive_folder_name?: string  // 归档文件夹名称，默认为"已发布"
  platforms?: string[]  // 可选：指定要检查的平台列表
}

export interface ArchivePublishedFilesResponse {
  success: boolean
  archived_count: number
  skipped_count: number
  archive_folder: string
  archived_files: string[]
  errors: string[]
}

export const batchRenameFiles = (data: BatchRenameRequest) => {
  return request<BatchRenameResponse>({
    url: '/api/v1/filesystem/batch-rename',
    method: 'post',
    data
  })
}

// 归档已发布文件
export const archivePublishedFiles = (data: ArchivePublishedFilesRequest, coreServiceId?: string) => {
  return request<ArchivePublishedFilesResponse>({
    url: '/api/v1/filesystem/archive-published',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 获取文件名列表（用于复制）
export const getFilenamesForCopy = (path: string) => {
  return request<string[]>({
    url: '/api/v1/filesystem/get-filenames',
    method: 'post',
    data: { path }
  })
}

// ==================== MD5记录管理API ====================

// 平台发布记录类型定义
export interface PlatformPublishRecord {
  platform: string  // 平台名称：youtube, tiktok, instagram, douyin, kuaishou, xiaohongshu, weibo
  is_published: boolean
  publish_date?: string
  publish_account?: string
  video_id?: string  // 平台上的视频ID
  video_url?: string  // 视频链接
  notes?: string
}

// MD5记录相关类型定义
export interface VideoMD5Record {
  file_path: string
  file_name: string
  md5_hash: string
  file_size: number
  duration?: number
  resolution?: string
  platform_records: PlatformPublishRecord[]  // 各平台发布记录
  notes?: string
  created_at?: string
  updated_at?: string
}

export interface VideoMD5RecordCreate {
  file_path: string
  file_name: string
  md5_hash: string
  file_size: number
  duration?: number
  resolution?: string
  notes?: string
}

export interface VideoMD5RecordUpdate {
  notes?: string
}

export interface VideoMD5RecordCreate {
  file_path: string
  file_name: string
  md5_hash: string
  file_size: number
  duration?: number
  resolution?: string
  notes?: string
}

export interface PlatformPublishUpdate {
  platform: string
  is_published: boolean
  publish_date?: string
  publish_account?: string
  video_id?: string
  video_url?: string
  notes?: string
}

export interface VideoMD5RecordListResponse {
  records: VideoMD5Record[]
  total: number
  page: number
  limit: number
}

export interface MD5CompareResult {
  file_name: string
  file_path: string
  md5_hash: string
  is_duplicate: boolean
  existing_record?: VideoMD5Record
  duplicate_info?: {
    platform?: string
    publish_date?: string
    publish_account?: string
    video_id?: string
    video_url?: string
    notes?: string
    published_platforms?: Array<{
      platform: string
      publish_date?: string
      publish_account?: string
      video_id?: string
      video_url?: string
      notes?: string
    }>
    total_published?: number
  }
}

export interface MD5CompareResponse {
  results: MD5CompareResult[]
  total_files: number
  duplicate_count: number
}

// 保存视频MD5记录
export const createVideoMD5Record = (record: VideoMD5RecordCreate) => {
  return request({
    url: '/api/v1/filesystem/md5-records',
    method: 'post',
    data: record
  })
}

// 更新视频MD5记录
export const updateVideoMD5Record = (md5Hash: string, updateData: VideoMD5RecordUpdate) => {
  return request({
    url: `/api/v1/filesystem/md5-records/${md5Hash}`,
    method: 'put',
    data: updateData
  })
}

// 更新平台发布状态
export const updatePlatformPublishStatus = (md5Hash: string, platform: string, platformData: PlatformPublishUpdate) => {
  return request({
    url: `/api/v1/filesystem/md5-records/${md5Hash}/platform/${platform}`,
    method: 'put',
    data: platformData
  })
}

// 获取平台发布状态
export const getPlatformPublishStatus = (md5Hash: string, platform: string) => {
  return request({
    url: `/api/v1/filesystem/md5-records/${md5Hash}/platform/${platform}`,
    method: 'get'
  })
}

// 检查平台是否已发布
export const checkPlatformPublished = (md5Hash: string, platform: string) => {
  return request({
    url: `/api/v1/filesystem/md5-records/${md5Hash}/platform/${platform}/published`,
    method: 'get'
  })
}

// 获取所有平台的发布状态
export const getAllPlatformPublishStatus = (md5Hash: string) => {
  return request({
    url: `/api/v1/filesystem/md5-records/${md5Hash}/platforms`,
    method: 'get'
  })
}

// 获取特定平台已发布的视频列表
export const getPublishedVideosByPlatform = (platform: string, page: number = 1, limit: number = 20) => {
  return request({
    url: `/api/v1/filesystem/md5-records/platform/${platform}/published`,
    method: 'get',
    params: { page, limit }
  })
}

// 获取特定平台已发布视频的MD5列表
export const getPlatformPublishedMD5List = (platform: string) => {
  return request({
    url: `/api/v1/filesystem/md5-records/platform/${platform}/published-md5-list`,
    method: 'get'
  })
}

// 批量检查多个平台的发布状态
export const batchCheckPlatformPublished = (md5Hashes: string[], platforms: string[]) => {
  return request({
    url: '/api/v1/filesystem/md5-records/batch-check-platforms',
    method: 'post',
    data: {
      md5_hashes: md5Hashes,
      platforms: platforms
    }
  })
}

// 获取视频MD5记录列表
export const getVideoMD5Records = (params?: {
  page?: number
  limit?: number
  folder_path?: string
}) => {
  return request<VideoMD5RecordListResponse>({
    url: '/api/v1/filesystem/md5-records',
    method: 'get',
    params
  })
}

// 批量保存文件夹中视频的MD5记录
export const batchSaveFolderMD5Records = (folderPath: string) => {
  return request({
    url: '/api/v1/filesystem/md5-records/batch-save',
    method: 'post',
    data: { path: folderPath }
  })
}

// MD5比对
export const compareFolderMD5Records = (folderPath: string, platform?: string) => {
  return request<MD5CompareResponse>({
    url: '/api/v1/filesystem/md5-records/compare',
    method: 'post',
    data: {
      folder_path: folderPath,
      platform: platform
    }
  })
}

// 批量删除文件或文件夹（物理删除，不删除MD5记录）
export const batchDeleteFiles = (filePaths: string[]) => {
  return request({
    url: '/api/v1/filesystem/md5-records/batch-delete',
    method: 'post',
    data: { file_paths: filePaths }
  })
}

// 删除单个文件或文件夹（物理删除，不删除MD5记录）
export const deleteFile = (filePath: string, coreServiceId?: string) => {
  return request({
    url: '/api/v1/filesystem/delete-files',
    method: 'post',
    data: { file_paths: [filePath] },
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 根据MD5哈希值获取记录
export const getVideoMD5RecordByHash = (md5Hash: string) => {
  return request({
    url: `/api/v1/filesystem/md5-records/${md5Hash}`,
    method: 'get'
  })
}

// 删除MD5记录
export const deleteVideoMD5Record = (md5Hash: string) => {
  return request({
    url: `/api/v1/filesystem/md5-records/${md5Hash}`,
    method: 'delete'
  })
}

// 调试MD5记录查询
export const debugMD5Records = (params?: {
  folder_path?: string
  md5_hash?: string
}) => {
  return request({
    url: '/api/v1/filesystem/debug/md5-records',
    method: 'get',
    params
  })
}

// 批量查询MD5记录（通过MD5哈希值列表）
export const batchGetMD5Records = (md5Hashes: string[]) => {
  return request({
    url: '/api/v1/filesystem/md5-records/batch-get',
    method: 'post',
    data: { md5_hashes: md5Hashes }
  })
}

// 视频合并相关接口
export interface VideoMergeRequest {
  folder_path: string
  target_duration_min: number  // 目标时长最小值（秒）
  target_duration_max: number  // 目标时长最大值（秒）
  enable_transitions: boolean  // 是否启用转场特效
  output_quality: 'high' | 'medium' | 'low'  // 输出质量
  max_videos_per_merge: number  // 每个合并视频最多包含的原视频数量
}

export interface VideoMergeResponse {
  success: boolean
  task_id: string
  message: string
  output_folder: string
  estimated_time: number  // 预计完成时间（秒）
}

export interface VideoMergeProgress {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number  // 0-100
  current_step: string
  total_videos: number
  processed_videos: number
  output_files: string[]
  error_message?: string
}

// 三拼视频相关接口
export interface TripleVideoMergeRequest {
  folder_path: string
  output_quality: 'high' | 'medium' | 'low'  // 输出质量
  video_duration_per_segment: number  // 每个视频片段播放时长（秒）
  transition_duration: number  // 转场时长（秒）
  enable_preview: boolean  // 是否生成预览
}

export interface TripleVideoMergeResponse {
  success: boolean
  task_id: string
  message: string
  output_folder: string
  estimated_time: number  // 预计完成时间（秒）
  preview_url?: string  // 预览视频URL
}

export interface TripleVideoMergeProgress {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number  // 0-100
  current_step: string
  total_videos: number
  processed_videos: number
  output_files: string[]
  preview_file?: string
  error_message?: string
}

// 创建视频合并任务
export const createVideoMergeTask = (data: VideoMergeRequest, coreServiceId?: string) => {
  return request<VideoMergeResponse>({
    url: '/api/v1/filesystem/merge-videos',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 获取视频合并任务进度
export const getVideoMergeProgress = (taskId: string) => {
  return request<VideoMergeProgress>({
    url: `/api/v1/filesystem/merge-videos/${taskId}/progress`,
    method: 'get'
  })
}

// 取消视频合并任务
export const cancelVideoMergeTask = (taskId: string) => {
  return request({
    url: `/api/v1/filesystem/merge-videos/${taskId}`,
    method: 'delete'
  })
}

// 获取所有视频合并任务
export const listVideoMergeTasks = () => {
  return request<VideoMergeProgress[]>({
    url: '/api/v1/filesystem/merge-videos',
    method: 'get'
  })
}

// 获取特定平台和Core服务的账号列表
export const getServiceAccounts = (platformId: string, coreServiceId: string, status: string = 'active') => {
  return request({
    url: `/api/v1/social/accounts`,
    method: 'get',
    params: {
      platform_id: platformId,
      core_service_id: coreServiceId,
      status: status
    }
  })
}

// 获取平台发布路径
export const listPlatformPaths = (platformId: string, coreServiceId: string, accountId: string) => {
  console.log('调用listPlatformPaths API:', {
    platformId,
    coreServiceId,
    accountId
  })

  return request({
    url: '/api/v1/social/publish-paths',
    method: 'get',
    params: {
      platform_id: platformId,
      core_service_id: coreServiceId,
      account_id: accountId
    }
  }).then(response => {
    console.log('listPlatformPaths API响应:', response)
    return response
  }).catch(error => {
    console.error('listPlatformPaths API错误:', error)
    throw error
  })
}

// 上传平台图标
export const uploadPlatformIcon = (file: File, platformId?: string) => {
  const formData = new FormData()
  formData.append('file', file)

  // 如果提供了平台ID，添加到表单数据中
  if (platformId) {
    formData.append('platform_id', platformId)
    console.log('上传图标，添加平台ID到表单:', platformId)
  }

  // 打印完整的表单数据，帮助调试
  console.log('上传图标表单数据:')
  try {
    // 使用any类型避免TypeScript错误
    const entries = (formData as any).entries()
    for (const pair of entries) {
      console.log(`${pair[0]}: ${pair[1]}`)
    }
  } catch (e) {
    console.log('无法打印表单数据:', e)
  }

  return request({
    url: '/api/v1/social/platforms/upload-icon',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 添加超时设置，图片上传可能需要更长时间
    timeout: 120000 // 2分钟，适应图片上传
  })
}

// 设备账号关联API

// 获取设备关联的账号
export const getDeviceAccounts = (deviceId: string, platformId?: string) => {
  const params = platformId ? { platform_id: platformId } : {}
  return request({
    url: `/api/v1/social/devices/${deviceId}/accounts`,
    method: 'get',
    params
  })
}

// 获取账号关联的设备
export const getAccountDevices = (accountId: string) => {
  console.log('获取账号关联的设备:', accountId);

  // 使用_id作为参数名，并确保URL中的accountId和params中的_id都是一样的
  return request({
    url: `/api/v1/social/accounts/${accountId}/devices`,
    method: 'get',
    params: {
      _id: accountId,  // 添加_id参数
      account_id: accountId  // 添加account_id参数
    }
  })
}

// 打开账号关联的设备
export const openAccountDevice = (accountId: string) => {
  return request<{success: boolean, message?: string}>({
    url: `/api/v1/social/accounts/${accountId}/open-device`,
    method: 'post'
  })
}

// 关联设备和账号
export const linkDeviceAccount = (data: {
  device_id: string
  account_id: string
  platform_id: string
  app_id: string
  settings?: {
    auto_login?: boolean
    keep_alive?: boolean
    notification?: boolean
  }
}) => {
  console.log('关联设备和账号:', data);

  return request({
    url: `/api/v1/social/devices/${data.device_id}/accounts`,
    method: 'post',
    data: {
      account_id: data.account_id,
      platform_id: data.platform_id,
      app_id: data.app_id,
      settings: data.settings
    }
  })
}

// 解除设备和账号关联
export const unlinkDeviceAccount = (deviceId: string, accountId: string) => {
  console.log('解除设备和账号关联:', deviceId, accountId);

  return request({
    url: `/api/v1/social/devices/${deviceId}/accounts/${accountId}`,
    method: 'delete'
  })
}

// 三拼视频相关API
// 创建三拼视频任务
export const createTripleVideoTask = (data: TripleVideoMergeRequest, coreServiceId?: string) => {
  return request<TripleVideoMergeResponse>({
    url: '/api/v1/filesystem/create-triple-video',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 获取三拼视频任务进度
export const getTripleVideoProgress = (taskId: string) => {
  return request<TripleVideoMergeProgress>({
    url: `/api/v1/filesystem/triple-video/${taskId}/progress`,
    method: 'get'
  })
}

// 取消三拼视频任务
export const cancelTripleVideoTask = (taskId: string) => {
  return request({
    url: `/api/v1/filesystem/triple-video/${taskId}`,
    method: 'delete'
  })
}

// 获取所有三拼视频任务
export const listTripleVideoTasks = () => {
  return request<TripleVideoMergeProgress[]>({
    url: '/api/v1/filesystem/triple-video',
    method: 'get'
  })
}

// ==================== 视频预览相关API ====================

// 视频缩略图请求接口
export interface VideoThumbnailRequest {
  video_path: string
  thumbnail_path?: string
  timestamp?: number
  max_width?: number
  max_height?: number
  quality?: number
  force_regenerate?: boolean
}

// 视频缩略图响应接口
export interface VideoThumbnailResponse {
  success: boolean
  error?: string
  thumbnail_path?: string
  thumbnail_url?: string
  thumbnail_size?: number
  actual_width?: number
  actual_height?: number
  generation_time_ms?: number
  from_cache?: boolean
}

// 视频预览信息请求接口
export interface VideoPreviewInfoRequest {
  video_path: string
  include_thumbnail?: boolean
  include_detailed_metadata?: boolean
}

// 视频预览信息响应接口
export interface VideoPreviewInfoResponse {
  success: boolean
  error?: string
  media_info?: {
    duration: number
    resolution: string
    video_codec: string
    audio_codec?: string
    frame_rate: number
    bitrate: number
  }
  thumbnail_info?: {
    thumbnail_path: string
    thumbnail_size: number
    width: number
    height: number
    exists: boolean
    generated_time: number
  }
  detailed_metadata?: {
    format: string
    creation_time?: string
    title?: string
    author?: string
    description?: string
    tags?: string[]
  }
}

// 视频预览片段请求接口
export interface VideoPreviewClipRequest {
  video_path: string
  preview_clip_path?: string
  start_time?: number
  duration?: number
  output_quality?: string
  force_regenerate?: boolean
}

// 视频预览片段响应接口
export interface VideoPreviewClipResponse {
  success: boolean
  error?: string
  preview_clip_path?: string
  preview_clip_size?: number
  generation_time_ms?: number
  from_cache?: boolean
}

// 批量视频预览信息请求接口
export interface BatchVideoPreviewRequest {
  video_paths: string[]
  include_thumbnail?: boolean
  include_detailed_metadata?: boolean
}

// 批量视频预览信息响应接口
export interface BatchVideoPreviewResponse {
  success: boolean
  error?: string
  results: Array<{
    video_path: string
    success: boolean
    error?: string
    media_info?: any
    thumbnail_info?: any
    detailed_metadata?: any
  }>
  successful_count: number
  failed_count: number
}

// 生成视频缩略图
export const generateVideoThumbnail = (data: VideoThumbnailRequest, coreServiceId?: string) => {
  return request<VideoThumbnailResponse>({
    url: '/api/v1/filesystem/video/thumbnail',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 获取视频预览信息
export const getVideoPreviewInfo = (data: VideoPreviewInfoRequest, coreServiceId?: string) => {
  return request<VideoPreviewInfoResponse>({
    url: '/api/v1/filesystem/video/preview-info',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 生成视频预览片段
export const generateVideoPreviewClip = (data: VideoPreviewClipRequest, coreServiceId?: string) => {
  return request<VideoPreviewClipResponse>({
    url: '/api/v1/filesystem/video/preview-clip',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 批量获取视频预览信息
export const getBatchVideoPreviewInfo = (data: BatchVideoPreviewRequest) => {
  return request<BatchVideoPreviewResponse>({
    url: '/api/v1/filesystem/video/batch-preview-info',
    method: 'post',
    data
  })
}

// ==================== 音频处理相关API ====================

// 批量生成字幕请求接口
export interface GenerateSubtitlesRequest {
  folder_path: string  // 文件夹路径
  output_format?: string  // 输出格式 (srt, vtt, txt)
  language?: string  // 语言代码 (auto, zh, en, etc.)
  model_size?: string  // Whisper模型大小 (tiny, base, small, medium, large)
  max_concurrent?: number  // 最大并发处理数
}

// 批量分离音频请求接口
export interface ExtractAudioRequest {
  folder_path: string  // 文件夹路径
  output_format?: string  // 输出音频格式 (wav, mp3, aac)
  quality?: string  // 音频质量 (high, medium, low)
  max_concurrent?: number  // 最大并发处理数
}

// 批量人声分离请求接口
export interface SeparateVocalsRequest {
  folder_path: string  // 文件夹路径
  separation_method?: string  // 分离方法 (ffmpeg, librosa)
  output_quality?: string  // 输出质量 (high, medium, low)
  max_concurrent?: number  // 最大并发处理数
}

// 批量替换音频请求接口
export interface ReplaceAudioRequest {
  folder_path: string  // 视频文件夹路径
  new_audio_path: string  // 新音频文件路径
  audio_volume?: number  // 音频音量 (0.0-2.0)
  fade_duration?: number  // 淡入淡出时长（秒）
  max_concurrent?: number  // 最大并发处理数
}

// 音频处理结果接口
export interface AudioProcessingResult {
  success: boolean
  input_file: string
  output_file: string
  error_message?: string
  processing_time_ms: number
  input_file_size: number
  output_file_size: number
  message: string
}

// 人声分离结果接口
export interface VocalSeparationResult {
  success: boolean
  input_file: string
  vocals_file: string
  instrumental_file: string
  error_message?: string
  processing_time_ms: number
  input_file_size: number
  vocals_file_size: number
  instrumental_file_size: number
  message: string
}

// 音频处理响应接口
export interface AudioProcessingResponse {
  success: boolean
  error?: string
  processed_files: number
  successful_count: number
  failed_count: number
  output_directory: string
  results: AudioProcessingResult[]
}

// 人声分离响应接口
export interface VocalSeparationResponse {
  success: boolean
  error?: string
  processed_files: number
  successful_count: number
  failed_count: number
  output_directory: string
  results: VocalSeparationResult[]
}

// 音频替换响应接口
export interface ReplaceAudioResponse {
  success: boolean
  error?: string
  processed_files: number
  successful_count: number
  failed_count: number
  output_directory: string
  new_audio_file: string
  results: AudioProcessingResult[]
}

// 批量生成字幕
export const generateSubtitlesBatch = (data: GenerateSubtitlesRequest, coreServiceId?: string) => {
  return request<AudioProcessingResponse>({
    url: '/api/v1/filesystem/generate-subtitles',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 批量分离音频
export const extractAudioBatch = (data: ExtractAudioRequest, coreServiceId?: string) => {
  return request<AudioProcessingResponse>({
    url: '/api/v1/filesystem/extract-audio',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 批量人声分离
export const separateVocalsBatch = (data: SeparateVocalsRequest, coreServiceId?: string) => {
  return request<VocalSeparationResponse>({
    url: '/api/v1/filesystem/separate-vocals',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 批量替换音频
export const replaceAudioBatch = (data: ReplaceAudioRequest, coreServiceId?: string) => {
  return request<ReplaceAudioResponse>({
    url: '/api/v1/filesystem/replace-audio',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// ==================== 预发布相关API ====================

// 预发布请求接口
export interface PrePublishRequest {
  file_paths: string[]  // 要预发布的文件路径列表
  current_folder: string  // 当前文件夹路径，用于确定根目录
}

// 预发布响应接口
export interface PrePublishResponse {
  success: boolean
  moved_count: number
  total_count: number
  publishing_folder: string  // publishing文件夹路径
  moved_files: string[]  // 成功移动的文件列表
  errors: string[]  // 错误信息列表
}

// 预发布文件
export const prePublishFiles = (data: PrePublishRequest, coreServiceId?: string) => {
  return request<PrePublishResponse>({
    url: '/api/v1/filesystem/pre-publish',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// ==================== 水印检测相关API ====================

// 水印检测请求接口
export interface WatermarkDetectionRequest {
  video_path: string  // 视频文件路径
  detection_mode?: string  // 检测模式 (auto, template, region)
  template_path?: string  // 水印模板路径（模板匹配模式使用）
  detection_region?: string  // 检测区域 (x,y,width,height)
  sensitivity?: number  // 检测敏感度 (0.0-1.0)
  save_detection_result?: boolean  // 是否保存检测结果图片
}

// 水印信息接口
export interface WatermarkInfo {
  watermark_type: string
  position: string
  confidence: number
  description: string
  time_range: string
}

// 水印检测响应接口
export interface WatermarkDetectionResponse {
  success: boolean
  error?: string
  watermark_detected: boolean
  watermarks: WatermarkInfo[]
  detection_result_path?: string
  detection_time_ms: number
}

// 检测视频水印
export const detectVideoWatermark = (data: WatermarkDetectionRequest, coreServiceId?: string) => {
  return request<WatermarkDetectionResponse>({
    url: '/api/v1/filesystem/watermark/detect',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 水印清除请求接口（单文件）
export interface WatermarkRemovalRequest {
  input_video_path: string  // 输入视频路径
  output_video_path: string  // 输出视频路径
  removal_mode?: string  // 清除模式 (auto, manual, inpaint)
  inpaint_method?: string  // 修复方法 (telea, ns)
  output_quality?: string  // 输出质量 (high, medium, low)
  preserve_encoding?: boolean  // 是否保持原始编码
  watermark_regions?: string[]  // 手动指定的水印区域
}

// 水印清除响应接口
export interface WatermarkRemovalResponse {
  success: boolean
  error?: string
  output_file_path: string
  processing_time_ms: number
  original_file_size: number
  output_file_size: number
}

// 批量水印处理请求接口
export interface BatchWatermarkRequest {
  input_folder_path: string  // 输入文件夹路径
  output_folder_path: string  // 输出文件夹路径
  process_mode: string  // 处理模式 (detect_only, remove_only, detect_and_remove)
  file_filters?: string[]  // 文件过滤器
  recursive?: boolean  // 是否递归处理子文件夹
  max_concurrent?: number  // 最大并发处理数
  detection_config: any  // 检测配置
  removal_config: any  // 清除配置
}

// 批量水印处理响应接口
export interface BatchWatermarkResponse {
  success: boolean
  error?: string
  task_id: string
  message: string
}

// 清除视频水印（单文件）
export const removeVideoWatermark = (data: WatermarkRemovalRequest, coreServiceId?: string) => {
  return request<WatermarkRemovalResponse>({
    url: '/api/v1/filesystem/watermark/remove',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 批量水印处理
export const batchWatermarkProcess = (data: BatchWatermarkRequest, coreServiceId?: string) => {
  return request<BatchWatermarkResponse>({
    url: '/api/v1/filesystem/watermark/batch',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// ==================== 视频处理相关API ====================

// 视频旋转请求接口
export interface VideoRotationRequest {
  video_paths: string[]  // 要旋转的视频文件路径列表
  rotation_angle: number  // 旋转角度（90: 向右90度, -90: 向左90度, 180: 180度）
  output_quality: 'high' | 'medium' | 'low'  // 输出质量
  overwrite_original: boolean  // 是否覆盖原文件
  output_suffix: string  // 输出文件名后缀（当不覆盖原文件时使用）
}

// 视频旋转响应接口
export interface VideoRotationResponse {
  success: boolean
  error?: string
  results: Array<{
    original_path: string
    output_path: string
    success: boolean
    error_message?: string
    processing_time_ms: number
    original_file_size: number
    output_file_size: number
  }>
  successful_count: number
  failed_count: number
  total_processing_time_ms: number
}

// 旋转视频
export const rotateVideos = (data: VideoRotationRequest, coreServiceId?: string) => {
  return request<VideoRotationResponse>({
    url: '/api/v1/filesystem/rotate-videos',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 视频片头片尾处理相关接口
export interface VideoIntroOutroRequest {
  folder_path: string
  selected_files?: string[]  // 选中的文件列表（可选，如果不提供则处理整个文件夹）
  intro_path?: string
  outro_path?: string
  output_folder?: string
  transition_effect: 'fade' | 'dissolve' | 'wipe' | 'slide' | 'zoom' | 'none'
  transition_duration: number
  output_quality: 'high' | 'medium' | 'low'
  max_concurrent: number
  overwrite_original: boolean
  output_suffix: string
}

export interface VideoIntroOutroResult {
  input_path: string
  output_path: string
  success: boolean
  error_message?: string
  processing_time_ms: number
  output_size: number
}

export interface VideoIntroOutroResponse {
  success: boolean
  error?: string
  results: VideoIntroOutroResult[]
  successful_count: number
  failed_count: number
  total_count: number
  total_processing_time_ms: number
  output_folder: string
  total_output_size: number
}

// 批量添加视频片头片尾
export const addIntroOutroBatch = (data: VideoIntroOutroRequest) => {
  return request<VideoIntroOutroResponse>({
    url: '/api/v1/filesystem/add-intro-outro',
    method: 'post',
    data
  })
}


