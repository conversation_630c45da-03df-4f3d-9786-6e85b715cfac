#!/usr/bin/env python3
"""
快速修复指定任务的Core服务配置
"""

import pymongo
import sys

def fix_task_core_service(task_id, core_service_id):
    """为指定任务设置Core服务ID"""
    try:
        # 连接MongoDB
        client = pymongo.MongoClient('mongodb://192.168.123.137:27017/')
        db = client['thunderhub']
        
        # 查找任务
        task = db.social_tasks.find_one({'task_id': task_id})
        
        if not task:
            print(f"❌ 未找到任务: {task_id}")
            return False
            
        print(f"=== 任务信息 ===")
        print(f"任务ID: {task.get('task_id')}")
        print(f"任务类型: {task.get('task_type')}")
        print(f"状态: {task.get('status')}")
        print(f"当前Core服务ID: {task.get('core_service_id', '❌ 未设置')}")
        print(f"父任务ID: {task.get('parent_task_id', '无')}")
        
        # 更新任务的Core服务配置
        update_result = db.social_tasks.update_one(
            {'task_id': task_id},
            {'$set': {'core_service_id': core_service_id}}
        )
        
        if update_result.modified_count > 0:
            print(f"✅ 成功为任务 {task_id} 设置Core服务ID: {core_service_id}")
            
            # 如果是主任务，也更新所有子任务
            if task.get('task_type') == 'main':
                subtasks_result = db.social_tasks.update_many(
                    {'parent_task_id': task_id},
                    {'$set': {'core_service_id': core_service_id}}
                )
                if subtasks_result.modified_count > 0:
                    print(f"✅ 同时更新了 {subtasks_result.modified_count} 个子任务的Core服务ID")
            
            return True
        else:
            print(f"⚠️ 任务 {task_id} 未被更新（可能已经有相同的Core服务ID）")
            return False
            
    except Exception as e:
        print(f"❌ 修复任务Core服务配置失败: {str(e)}")
        return False
    finally:
        if 'client' in locals():
            client.close()

def check_task(task_id):
    """检查任务的Core服务配置"""
    try:
        # 连接MongoDB
        client = pymongo.MongoClient('mongodb://192.168.123.137:27017/')
        db = client['thunderhub']
        
        # 查找任务
        task = db.social_tasks.find_one({'task_id': task_id})
        
        if not task:
            print(f"❌ 未找到任务: {task_id}")
            return
            
        print(f"=== 检查任务 {task_id} ===")
        print(f"任务类型: {task.get('task_type')}")
        print(f"状态: {task.get('status')}")
        print(f"Core服务ID: {task.get('core_service_id', '❌ 未设置')}")
        print(f"父任务ID: {task.get('parent_task_id', '无')}")
        
        # 检查是否需要修复
        if not task.get('core_service_id'):
            print("❌ 任务缺少core_service_id，需要修复")
            
            # 尝试推断合适的Core服务ID
            suggested_core_id = None
            
            # 如果是子任务，检查父任务
            if task.get('parent_task_id'):
                parent_task = db.social_tasks.find_one({'task_id': task['parent_task_id']})
                if parent_task and parent_task.get('core_service_id'):
                    suggested_core_id = parent_task['core_service_id']
                    print(f"建议从父任务继承Core服务ID: {suggested_core_id}")
            
            # 检查设备映射
            if not suggested_core_id and task.get('account_id') and task.get('platform_id'):
                mapping = db.device_account_mappings.find_one({
                    'account_id': task['account_id'],
                    'platform_id': task['platform_id'],
                    'status': 'active'
                })
                if mapping and mapping.get('core_service_id'):
                    suggested_core_id = mapping['core_service_id']
                    print(f"建议从设备映射获取Core服务ID: {suggested_core_id}")
            
            if suggested_core_id:
                print(f"\n建议修复命令:")
                print(f"python quick_fix_task.py fix {task_id} {suggested_core_id}")
            else:
                print("无法自动推断合适的Core服务ID，需要手动指定")
                print("可用的Core服务ID示例: thunderhub-core-server1, thunderhub-core-server2")
        else:
            print(f"✅ 任务已有Core服务ID: {task.get('core_service_id')}")
            
    except Exception as e:
        print(f"❌ 检查任务失败: {str(e)}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法:")
        print("  检查任务: python quick_fix_task.py check <task_id>")
        print("  修复任务: python quick_fix_task.py fix <task_id> <core_service_id>")
        print("")
        print("示例:")
        print("  python quick_fix_task.py check 93badfee-12f3-4093-bd74-29ccf672986b")
        print("  python quick_fix_task.py fix 93badfee-12f3-4093-bd74-29ccf672986b thunderhub-core-server1")
        sys.exit(1)
    
    action = sys.argv[1]
    task_id = sys.argv[2]
    
    if action == "check":
        check_task(task_id)
    elif action == "fix":
        if len(sys.argv) < 4:
            print("❌ 修复操作需要指定Core服务ID")
            print("用法: python quick_fix_task.py fix <task_id> <core_service_id>")
            sys.exit(1)
        core_service_id = sys.argv[3]
        success = fix_task_core_service(task_id, core_service_id)
        if success:
            print("✅ 修复完成")
        else:
            print("❌ 修复失败")
            sys.exit(1)
    else:
        print(f"❌ 未知操作: {action}")
        print("支持的操作: check, fix")
        sys.exit(1)
