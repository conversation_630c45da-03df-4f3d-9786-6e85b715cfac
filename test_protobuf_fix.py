#!/usr/bin/env python3
"""
测试protobuf修复
验证WorkflowConfigRequest是否可以正确导入和使用
"""

import sys
import os

# 添加backend路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_protobuf_import():
    """测试protobuf导入"""
    try:
        print("1. 测试protobuf导入...")
        
        # 测试导入task_pb2
        from backend.app.proto import task_pb2
        print("✅ 成功导入 task_pb2")
        
        # 测试WorkflowConfigRequest是否存在
        if hasattr(task_pb2, 'WorkflowConfigRequest'):
            print("✅ WorkflowConfigRequest 类存在")
            
            # 尝试创建实例
            request = task_pb2.WorkflowConfigRequest(
                platform_id="youtube",
                content_type="shorts"
            )
            print(f"✅ 成功创建 WorkflowConfigRequest 实例: platform_id={request.platform_id}, content_type={request.content_type}")
            return True
        else:
            print("❌ WorkflowConfigRequest 类不存在")
            return False
            
    except Exception as e:
        print(f"❌ protobuf导入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_grpc_stub_import():
    """测试gRPC stub导入"""
    try:
        print("2. 测试gRPC stub导入...")
        
        from backend.app.proto import task_pb2_grpc
        print("✅ 成功导入 task_pb2_grpc")
        
        # 检查TaskServiceStub是否存在
        if hasattr(task_pb2_grpc, 'TaskServiceStub'):
            print("✅ TaskServiceStub 类存在")
            return True
        else:
            print("❌ TaskServiceStub 类不存在")
            return False
            
    except Exception as e:
        print(f"❌ gRPC stub导入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_client_import():
    """测试客户端导入"""
    try:
        print("3. 测试客户端导入...")
        
        from backend.app.core.client import TaskServiceClient
        print("✅ 成功导入 TaskServiceClient")
        return True
        
    except Exception as e:
        print(f"❌ 客户端导入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试protobuf修复")
    print("=" * 50)
    
    # 测试protobuf导入
    protobuf_ok = test_protobuf_import()
    print()
    
    # 测试gRPC stub导入
    grpc_ok = test_grpc_stub_import()
    print()
    
    # 测试客户端导入
    client_ok = test_client_import()
    print()
    
    # 总结
    print("=" * 50)
    print("测试结果总结:")
    print(f"Protobuf导入: {'✅ 成功' if protobuf_ok else '❌ 失败'}")
    print(f"gRPC Stub导入: {'✅ 成功' if grpc_ok else '❌ 失败'}")
    print(f"客户端导入: {'✅ 成功' if client_ok else '❌ 失败'}")
    
    if protobuf_ok and grpc_ok and client_ok:
        print("🎉 protobuf修复成功！现在可以正常使用WorkflowConfigRequest")
        print("请重启Backend服务以应用更改")
    else:
        print("❌ protobuf修复失败，需要进一步检查")

if __name__ == "__main__":
    main()
