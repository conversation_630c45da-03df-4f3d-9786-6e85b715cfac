#!/usr/bin/env python3
"""
测试工作流修复
验证平台ID映射和配置文件读取是否正常工作
"""

import asyncio
import requests
import json
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId

async def create_test_task_with_correct_data():
    """创建带有正确数据的测试任务"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient("mongodb://192.168.123.137:27017")
        db = client["social_media_automation"]
        
        # 查找YouTube平台的ObjectId
        youtube_platform = await db.social_platforms.find_one({"id": "youtube"})
        if not youtube_platform:
            print("❌ 未找到YouTube平台，请先初始化数据库")
            return False
            
        youtube_platform_id = str(youtube_platform["_id"])
        print(f"✅ 找到YouTube平台ID: {youtube_platform_id}")
        
        # 创建测试任务
        test_task = {
            "task_id": "test_workflow_fix_456",
            "platform_id": youtube_platform_id,  # 使用正确的ObjectId
            "workflow_name": "YouTube短视频上传",
            "status": "pending",
            "metadata": {
                "contentType": "shorts",  # 明确指定内容类型
                "title": "测试短视频",
                "description": "这是一个测试短视频"
            },
            "created_at": "2024-01-01T10:00:00Z"
        }
        
        # 删除可能存在的旧测试任务
        await db.social_tasks.delete_many({"task_id": "test_workflow_fix_456"})
        
        # 插入新的测试任务
        result = await db.social_tasks.insert_one(test_task)
        print(f"✅ 创建测试任务成功: {result.inserted_id}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试任务失败: {str(e)}")
        return False

def test_workflow_api():
    """测试工作流API"""
    try:
        task_id = "test_workflow_fix_456"
        url = f"http://localhost:8000/api/v1/workflow/{task_id}/workflow"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=15)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功")
            print(f"数据源: {data.get('source', 'unknown')}")
            
            if data.get('success') and data.get('data'):
                workflow_data = data['data']
                print(f"工作流名称: {workflow_data.get('workflow_name')}")
                print(f"总步骤数: {workflow_data.get('total_steps')}")
                
                steps = workflow_data.get('steps', [])
                print(f"步骤详情:")
                for i, step in enumerate(steps[:8]):  # 显示前8个步骤
                    name = step.get('name', 'Unknown')
                    action = step.get('action', 'unknown')
                    element = step.get('element', '')
                    print(f"  步骤 {i+1}: {name} (Action: {action}, Element: {element})")
                
                # 检查是否是真实配置
                real_config_indicators = [
                    len(steps) > 8,  # 真实配置通常有更多步骤
                    any(step.get('action') in ['click', 'input', 'wait'] for step in steps),  # 有真实的action
                    any(step.get('element') for step in steps),  # 有element定义
                    workflow_data.get('workflow_name') != '未知工作流'  # 有真实的工作流名称
                ]
                
                if all(real_config_indicators):
                    print(f"🎉 成功获取到真实的工作流配置！")
                    return True
                elif any(real_config_indicators):
                    print(f"⚠️ 部分获取到真实配置，但可能不完整")
                    return True
                else:
                    print(f"❌ 仍然是模拟数据")
                    return False
            else:
                print(f"❌ API返回数据格式错误")
                return False
        elif response.status_code == 404:
            print(f"❌ 任务不存在")
            return False
        else:
            print(f"❌ API调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

async def cleanup_test_task():
    """清理测试任务"""
    try:
        client = AsyncIOMotorClient("mongodb://192.168.123.137:27017")
        db = client["social_media_automation"]
        
        result = await db.social_tasks.delete_many({"task_id": "test_workflow_fix_456"})
        print(f"✅ 清理测试任务成功，删除了 {result.deleted_count} 个任务")
        
        await client.close()
        
    except Exception as e:
        print(f"⚠️ 清理测试任务失败: {str(e)}")

async def main():
    """主函数"""
    print("开始测试工作流修复")
    
    # 1. 创建测试任务
    print("1. 创建带有正确数据的测试任务")
    if not await create_test_task_with_correct_data():
        print("创建测试任务失败，终止测试")
        return
    
    # 等待一下确保任务已保存
    await asyncio.sleep(2)
    
    # 2. 测试API
    print("2. 测试工作流配置API")
    success = test_workflow_api()
    
    # 3. 清理测试数据
    print("3. 清理测试数据")
    await cleanup_test_task()
    
    # 4. 输出结果
    if success:
        print("🎉 修复成功！Backend能够正确获取真实工作流配置")
        print("✅ 平台ID映射正常工作")
        print("✅ 配置文件读取正常工作")
    else:
        print("❌ 修复失败！需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
