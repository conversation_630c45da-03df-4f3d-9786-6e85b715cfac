#!/usr/bin/env python3
"""
测试任务客户端修复
验证TaskServiceClient和CoreClient的方法调用是否正确
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
import logging
from app.core.client import TaskServiceClient, CoreClient
from app.core.grpc_client import get_task_client
from app.core.service_discovery import get_core_client

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_task_service_client():
    """测试TaskServiceClient的方法签名"""
    try:
        logger.info("=== 测试TaskServiceClient ===")
        
        # 创建TaskServiceClient实例
        client = TaskServiceClient("localhost", 50051)
        
        # 检查方法签名
        import inspect
        
        # 检查create_task方法
        create_task_sig = inspect.signature(client.create_task)
        logger.info(f"TaskServiceClient.create_task 方法签名: {create_task_sig}")
        logger.info(f"参数数量: {len(create_task_sig.parameters)}")
        
        # 检查start_task方法
        start_task_sig = inspect.signature(client.start_task)
        logger.info(f"TaskServiceClient.start_task 方法签名: {start_task_sig}")
        logger.info(f"参数数量: {len(start_task_sig.parameters)}")
        
        # 检查是否有task_client属性
        has_task_client = hasattr(client, 'task_client')
        logger.info(f"TaskServiceClient 是否有 task_client 属性: {has_task_client}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试TaskServiceClient失败: {str(e)}")
        return False

def test_core_client():
    """测试CoreClient的方法签名"""
    try:
        logger.info("=== 测试CoreClient ===")
        
        # 创建CoreClient实例
        client = CoreClient("localhost", 50051)
        
        # 检查方法签名
        import inspect
        
        # 检查create_task方法
        create_task_sig = inspect.signature(client.create_task)
        logger.info(f"CoreClient.create_task 方法签名: {create_task_sig}")
        logger.info(f"参数数量: {len(create_task_sig.parameters)}")
        
        # 检查start_task方法
        start_task_sig = inspect.signature(client.start_task)
        logger.info(f"CoreClient.start_task 方法签名: {start_task_sig}")
        logger.info(f"参数数量: {len(start_task_sig.parameters)}")
        
        # 检查是否有task_client属性
        has_task_client = hasattr(client, 'task_client')
        logger.info(f"CoreClient 是否有 task_client 属性: {has_task_client}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试CoreClient失败: {str(e)}")
        return False

async def test_client_detection():
    """测试客户端类型检测逻辑"""
    try:
        logger.info("=== 测试客户端类型检测 ===")
        
        # 测试TaskServiceClient
        task_client = TaskServiceClient("localhost", 50051)
        has_task_client_attr = hasattr(task_client, 'task_client')
        logger.info(f"TaskServiceClient 检测结果: hasattr(client, 'task_client') = {has_task_client_attr}")
        
        # 测试CoreClient
        core_client = CoreClient("localhost", 50051)
        has_task_client_attr = hasattr(core_client, 'task_client')
        logger.info(f"CoreClient 检测结果: hasattr(client, 'task_client') = {has_task_client_attr}")
        
        # 模拟修复后的调用逻辑
        def simulate_create_task_call(client, task_id, task_data):
            """模拟修复后的create_task调用逻辑"""
            if hasattr(client, 'task_client'):
                logger.info(f"检测到CoreClient，使用两参数调用: create_task('{task_id}', task_data)")
                return f"CoreClient.create_task('{task_id}', task_data)"
            else:
                logger.info(f"检测到TaskServiceClient，使用单参数调用: create_task(task_data)")
                task_data["task_id"] = task_id
                return f"TaskServiceClient.create_task(task_data with task_id='{task_id}')"
        
        # 测试调用逻辑
        test_task_id = "test-task-123"
        test_task_data = {"platform_id": "youtube", "account_id": "test"}
        
        logger.info("--- 测试TaskServiceClient调用 ---")
        result1 = simulate_create_task_call(task_client, test_task_id, test_task_data.copy())
        logger.info(f"结果: {result1}")
        
        logger.info("--- 测试CoreClient调用 ---")
        result2 = simulate_create_task_call(core_client, test_task_id, test_task_data.copy())
        logger.info(f"结果: {result2}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试客户端类型检测失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始测试任务客户端修复")
    
    success_count = 0
    total_tests = 3
    
    # 测试TaskServiceClient
    if await test_task_service_client():
        success_count += 1
        logger.info("✅ TaskServiceClient测试通过")
    else:
        logger.error("❌ TaskServiceClient测试失败")
    
    # 测试CoreClient
    if test_core_client():
        success_count += 1
        logger.info("✅ CoreClient测试通过")
    else:
        logger.error("❌ CoreClient测试失败")
    
    # 测试客户端检测逻辑
    if await test_client_detection():
        success_count += 1
        logger.info("✅ 客户端检测逻辑测试通过")
    else:
        logger.error("❌ 客户端检测逻辑测试失败")
    
    # 总结
    logger.info(f"=== 测试总结 ===")
    logger.info(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！修复应该有效")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        sys.exit(1)
