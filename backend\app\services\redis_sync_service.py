"""
Redis同步服务
负责从Redis获取设备状态并通过WebSocket推送给前端
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional
import redis.asyncio as redis
from datetime import datetime

from app.services.device_history_service import DeviceHistoryService

logger = logging.getLogger(__name__)

class RedisSyncService:
    """Redis同步服务类"""

    def __init__(self,
                 redis_url: str,
                 socketio,
                 mongo_db,
                 snapshot_interval: int = 300):
        """初始化Redis同步服务

        Args:
            redis_url: Redis服务URL
            socketio: Socket.IO实例，用于推送状态给前端
            mongo_db: MongoDB数据库实例，用于保存状态快照
            snapshot_interval: 快照间隔（秒），默认300秒（5分钟）
        """
        self.redis_url = redis_url
        self.socketio = socketio
        self.mongo_db = mongo_db
        self.snapshot_interval = snapshot_interval
        self.redis_client = None
        self.pubsub = None
        self.is_running = False
        self.sync_task = None
        self.snapshot_task = None

        # 🔧 重要修复：生成Backend实例唯一标识
        import uuid
        import os
        self.backend_instance_id = os.environ.get("BACKEND_INSTANCE_ID", f"backend-{uuid.uuid4().hex[:8]}")

        # 初始化设备历史服务
        self.history_service = DeviceHistoryService(mongo_db)

        logger.info(f"Redis同步服务初始化，Backend实例ID: {self.backend_instance_id}，快照间隔: {snapshot_interval}秒")

    async def start(self) -> None:
        """启动同步服务"""
        if self.is_running:
            logger.warning("Redis同步服务已在运行中")
            return

        try:
            logger.info(f"正在连接Redis: {self.redis_url}")

            # 🔧 关键修复：使用异步Redis客户端
            import redis.asyncio as aioredis
            self.redis_client = aioredis.Redis.from_url(self.redis_url)

            # 测试连接
            try:
                ping_result = await self.redis_client.ping()
                logger.info(f"Redis连接成功，ping结果: {ping_result}")
            except Exception as e:
                logger.error(f"Redis ping测试失败: {str(e)}")
                raise

            # 检查Redis中的设备数据
            try:
                # 获取与设备相关的键
                device_keys = await self.redis_client.keys("device:*")
                if device_keys:
                    logger.info(f"Redis中找到{len(device_keys)}个设备相关的键")
                    # 显示前5个键
                    if len(device_keys) > 0:
                        logger.info(f"设备键示例: {device_keys[:5]}")
                else:
                    logger.warning("Redis中没有找到任何设备相关的键")

                # 检查设备集合
                device_set = await self.redis_client.exists("devices:all")
                if device_set:
                    device_count = await self.redis_client.scard("devices:all")
                    logger.info(f"Redis中的'devices:all'集合包含{device_count}个设备")
                else:
                    logger.warning("Redis中没有找到'devices:all'集合")

                # 获取所有键
                all_keys = await self.redis_client.keys("*")
                logger.info(f"Redis中共有{len(all_keys)}个键")
                # 显示前20个键
                if len(all_keys) > 0:
                    logger.info(f"Redis键示例: {all_keys[:20]}")
            except Exception as e:
                logger.error(f"检查Redis设备数据失败: {str(e)}")
                # 继续执行，不抛出异常

            # 初始化发布/订阅
            self.pubsub = self.redis_client.pubsub()

            # 订阅设备变更频道
            try:
                await self.pubsub.subscribe("device:all:changes")
                logger.info("已订阅设备变更频道 'device:all:changes'")

                # 订阅任务状态频道
                await self.pubsub.psubscribe("task:*:status")
                logger.info("已订阅任务状态频道 'task:*:status'")

                # 检查订阅状态
                try:
                    # 尝试获取已订阅的频道
                    # 不同版本的Redis客户端可能有不同的API
                    if hasattr(self.pubsub, 'channels') and callable(getattr(self.pubsub, 'channels', None)):
                        # 如果channels是方法
                        channels = await self.pubsub.channels()
                        logger.info(f"当前订阅的频道: {channels}")
                    elif hasattr(self.pubsub, 'channels') and isinstance(self.pubsub.channels, dict):
                        # 如果channels是字典属性
                        channels = list(self.pubsub.channels.keys())
                        logger.info(f"当前订阅的频道: {channels}")
                    elif hasattr(self.pubsub, 'patterns'):
                        # 尝试获取patterns
                        patterns = self.pubsub.patterns
                        logger.info(f"当前订阅的模式: {patterns}")
                    else:
                        logger.warning("无法获取已订阅的频道信息")

                    # 同时显示patterns信息（如果存在）
                    if hasattr(self.pubsub, 'patterns'):
                        patterns = self.pubsub.patterns
                        logger.info(f"当前订阅的模式: {patterns}")

                except Exception as e:
                    logger.error(f"获取订阅频道信息异常: {str(e)}")
            except Exception as e:
                logger.error(f"订阅设备变更频道失败: {str(e)}")
                raise

            # 启动同步任务
            self.is_running = True
            self.sync_task = asyncio.create_task(self._sync_loop())
            self.snapshot_task = asyncio.create_task(self._snapshot_loop())

            logger.info("🚀 Redis同步服务已启动")
            logger.info("📡 正在监听任务状态频道: task:*:status")
            logger.info("📡 正在监听设备变更频道: device:all:changes")
            logger.info("🔄 自动子任务调度功能已激活")

            # 🔧 强制输出到控制台
            print("🚀 Redis同步服务已启动")
            print("📡 正在监听任务状态频道: task:*:status")
            print("📡 正在监听设备变更频道: device:all:changes")
            print("🔄 自动子任务调度功能已激活")

        except Exception as e:
            logger.error(f"启动Redis同步服务异常: {str(e)}", exc_info=True)
            if self.redis_client:
                try:
                    await self.redis_client.close()
                except Exception as close_error:
                    logger.error(f"关闭Redis连接异常: {str(close_error)}")
                self.redis_client = None

    async def stop(self) -> None:
        """停止同步服务"""
        if not self.is_running:
            return

        self.is_running = False

        # 取消同步任务
        if self.sync_task:
            self.sync_task.cancel()
            try:
                await self.sync_task
            except asyncio.CancelledError:
                pass
            self.sync_task = None

        # 取消快照任务
        if self.snapshot_task:
            self.snapshot_task.cancel()
            try:
                await self.snapshot_task
            except asyncio.CancelledError:
                pass
            self.snapshot_task = None

        # 关闭发布/订阅
        if self.pubsub:
            await self.pubsub.unsubscribe()
            await self.pubsub.close()
            self.pubsub = None

        # 关闭Redis连接
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None

        logger.info("Redis同步服务已停止")

    async def _sync_loop(self) -> None:
        """同步循环，监听Redis发布/订阅消息"""
        try:
            logger.info("开始监听设备状态变更")

            # 检查订阅状态
            channels = []
            try:
                # 尝试获取已订阅的频道
                # 不同版本的Redis客户端可能有不同的API
                if hasattr(self.pubsub, 'channels') and callable(getattr(self.pubsub, 'channels', None)):
                    # 如果channels是方法
                    channels = await self.pubsub.channels()
                elif hasattr(self.pubsub, 'channels') and isinstance(self.pubsub.channels, dict):
                    # 如果channels是字典属性
                    channels = list(self.pubsub.channels.keys())
                elif hasattr(self.pubsub, 'patterns'):
                    # 尝试获取patterns
                    patterns = self.pubsub.patterns
                    logger.info(f"当前订阅的模式: {patterns}")
                else:
                    logger.warning("无法获取已订阅的频道信息")
            except Exception as e:
                logger.error(f"获取订阅频道信息异常: {str(e)}")

            logger.info(f"当前订阅的频道: {channels}")

            # 如果没有订阅任何频道，重新订阅
            if not channels:
                logger.warning("没有订阅任何频道，尝试重新订阅")
                try:
                    await self.pubsub.subscribe("device:all:changes")
                    logger.info("已重新订阅设备变更频道")
                except Exception as e:
                    logger.error(f"重新订阅频道失败: {str(e)}")

            # 记录循环开始时间
            start_time = time.time()
            message_count = 0
            last_log_time = start_time

            while self.is_running:
                try:
                    # 获取消息
                    message = await self.pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)

                    if message:
                        # 处理消息
                        await self._handle_message(message)
                        message_count += 1

                    # 每60秒记录一次状态
                    current_time = time.time()
                    if current_time - last_log_time > 60:
                        elapsed = current_time - start_time
                        logger.info(f"同步循环运行中: 已处理{message_count}条消息，运行时间: {elapsed:.1f}秒")
                        last_log_time = current_time

                        # 检查订阅状态
                        try:
                            channels = []
                            # 尝试获取已订阅的频道
                            # 不同版本的Redis客户端可能有不同的API
                            if hasattr(self.pubsub, 'channels') and callable(getattr(self.pubsub, 'channels', None)):
                                # 如果channels是方法
                                channels = await self.pubsub.channels()
                            elif hasattr(self.pubsub, 'channels') and isinstance(self.pubsub.channels, dict):
                                # 如果channels是字典属性
                                channels = list(self.pubsub.channels.keys())
                            elif hasattr(self.pubsub, 'patterns'):
                                # 尝试获取patterns
                                patterns = self.pubsub.patterns
                                logger.info(f"当前订阅的模式: {patterns}")
                            else:
                                logger.warning("无法获取已订阅的频道信息")

                            logger.info(f"当前订阅的频道: {channels}")

                            # 同时显示patterns信息
                            if hasattr(self.pubsub, 'patterns'):
                                patterns = self.pubsub.patterns
                                logger.info(f"当前订阅的模式: {patterns}")

                            # 如果没有订阅任何频道，重新订阅
                            if not channels:
                                logger.warning("没有订阅任何频道，尝试重新订阅")
                                await self.pubsub.subscribe("device:all:changes")
                                logger.info("已重新订阅设备变更频道")
                        except Exception as e:
                            logger.error(f"检查订阅状态失败: {str(e)}")

                    # 短暂休眠，避免CPU占用过高
                    await asyncio.sleep(0.01)

                except redis.RedisError as e:
                    logger.error(f"Redis操作异常: {str(e)}")
                    # 短暂休眠后继续
                    await asyncio.sleep(1.0)
                except Exception as e:
                    logger.error(f"处理消息异常: {str(e)}", exc_info=True)
                    # 短暂休眠后继续
                    await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            logger.info("同步循环被取消")
            raise
        except Exception as e:
            logger.error(f"同步循环异常: {str(e)}", exc_info=True)
            # 出现异常后尝试重启同步任务
            if self.is_running:
                logger.info("尝试重启同步任务")
                self.sync_task = asyncio.create_task(self._sync_loop())

    async def _snapshot_loop(self) -> None:
        """快照循环，定期将Redis数据保存到MongoDB"""
        try:
            logger.info(f"开始设备状态快照，间隔: {self.snapshot_interval}秒")

            while self.is_running:
                # 等待指定时间
                await asyncio.sleep(self.snapshot_interval)

                if self.is_running:  # 再次检查，避免在sleep期间被停止
                    # 执行快照
                    await self._take_snapshot()

        except asyncio.CancelledError:
            logger.info("快照循环被取消")
            raise
        except Exception as e:
            logger.error(f"快照循环异常: {str(e)}", exc_info=True)
            # 出现异常后尝试重启快照任务
            if self.is_running:
                logger.info("尝试重启快照任务")
                self.snapshot_task = asyncio.create_task(self._snapshot_loop())

    async def _handle_message(self, message: Dict[str, Any]) -> None:
        """处理Redis发布/订阅消息

        Args:
            message: Redis消息
        """
        try:
            # 记录收到的消息类型和频道
            logger.debug(f"收到Redis消息: 类型={message.get('type')}, 频道={message.get('channel')}")

            # 🔧 强制输出到控制台
            print(f"🔔 收到Redis消息: 类型={message.get('type')}, 频道={message.get('channel')}")

            # 解析消息 - 同时处理message和pmessage类型
            if message["type"] in ["message", "pmessage"]:
                channel = message["channel"]
                logger.info(f"处理频道 {channel} 的消息（类型: {message['type']}）")
                print(f"📨 处理频道 {channel} 的消息（类型: {message['type']}）")

                # 处理设备状态变更消息
                if channel == b"device:all:changes" or (isinstance(channel, bytes) and channel.startswith(b"device:") and channel.endswith(b":changes")):
                    try:
                        # 从频道名称中提取Core ID
                        core_id = "default"
                        if channel != b"device:all:changes":
                            # 频道格式为"device:{core_id}:changes"
                            channel_str = channel.decode('utf-8')
                            parts = channel_str.split(':')
                            if len(parts) == 3 and parts[0] == 'device' and parts[2] == 'changes':
                                core_id = parts[1]

                        data = json.loads(message["data"])
                        changes = data.get("changes", [])
                        timestamp = data.get("timestamp", int(time.time()))

                        logger.info(f"收到{len(changes)}个设备状态变更，Core: {core_id}，时间戳: {timestamp}")

                        if changes:
                            # 更新设备状态到数据库
                            for change in changes:
                                try:
                                    device_id = change.get("device_id")
                                    if not device_id:
                                        logger.warning(f"跳过没有device_id的变更: {change}")
                                        continue

                                    # 更新设备状态
                                    new_status = change.get("new_status")
                                    old_status = change.get("old_status", "unknown")
                                    change_timestamp = change.get("timestamp", timestamp)

                                    logger.info(f"设备 {device_id}（Core: {core_id}） 状态变更: {old_status} -> {new_status}")

                                    # 更新MongoDB
                                    await self._update_device_in_db(device_id, new_status, change_timestamp, core_id)

                                    # 通过WebSocket推送状态更新
                                    await self.socketio.emit(
                                        "device_status_update",
                                        {
                                            "id": device_id,
                                            "core_id": core_id,
                                            "status": new_status,
                                            "updated_at": datetime.fromtimestamp(change_timestamp).isoformat()
                                        }
                                    )
                                    logger.debug(f"已通过WebSocket推送设备 {device_id}（Core: {core_id}） 的状态更新")
                                except Exception as e:
                                    logger.error(f"处理设备 {device_id if 'device_id' in locals() else 'unknown'}（Core: {core_id}） 状态变更异常: {str(e)}")
                        else:
                            logger.warning(f"收到的变更列表为空（Core: {core_id}）")
                    except json.JSONDecodeError as e:
                        logger.error(f"解析消息数据JSON失败: {str(e)}")
                        logger.debug(f"原始消息数据: {message.get('data', '')[:100]}...")
                # 处理任务状态消息
                elif isinstance(channel, bytes) and channel.startswith(b"task:") and channel.endswith(b":status"):
                    try:
                        # 从频道名称中提取任务ID
                        # 频道格式为"task:{task_id}:status"
                        channel_str = channel.decode('utf-8')
                        parts = channel_str.split(':')
                        if len(parts) == 3 and parts[0] == 'task' and parts[2] == 'status':
                            task_id = parts[1]

                            # 🔧 重要修复：检查任务归属，只处理属于当前Backend实例的任务
                            task_owner = await self._get_task_owner(task_id)
                            logger.info(f"🔍 任务{task_id}归属检查: 当前Backend={self.backend_instance_id}, 任务归属={task_owner}")

                            if task_owner and task_owner != self.backend_instance_id:
                                logger.info(f"⏭️ 跳过任务{task_id}状态更新，归属于其他Backend实例: {task_owner}")
                                return  # 跳过处理，直接返回
                            elif task_owner is None:
                                logger.info(f"ℹ️ 任务{task_id}无归属信息，当前Backend({self.backend_instance_id})将处理")
                            else:
                                logger.info(f"✅ 任务{task_id}归属当前Backend({self.backend_instance_id})，正常处理")

                            # 解析任务状态数据
                            task_data_str = message.get("data", "{}")
                            if isinstance(task_data_str, bytes):
                                task_data_str = task_data_str.decode('utf-8')

                            task_data = json.loads(task_data_str)

                            # 确保task_id字段存在
                            if "task_id" not in task_data:
                                task_data["task_id"] = task_id

                            # 🔧 添加详细日志
                            task_status = task_data.get('status', 'unknown')
                            task_progress = task_data.get('progress', 0)
                            logger.info(f"🔔 Backend({self.backend_instance_id}) 处理任务{task_id}状态更新: {task_status} (进度: {task_progress}%)")
                            logger.info(f"📋 任务数据: {json.dumps(task_data, ensure_ascii=False)}")

                            # 🔧 重要修复：先更新MongoDB，再处理任务完成事件
                            # 这样确保_handle_task_completed中的查询能获取到最新状态

                            # 通过WebSocket推送任务状态更新
                            await self.socketio.emit(
                                "task_status_update",
                                task_data,
                                room=f"task_{task_id}"  # 发送到特定任务的房间
                            )
                            logger.debug(f"已通过WebSocket推送任务{task_id}的状态更新")

                            # 同时更新MongoDB中的任务状态
                            try:
                                if self.mongo_db is not None:
                                    # 🔧 修复时间记录：统一使用ISO格式字符串
                                    now_iso = datetime.now().isoformat()

                                    # 更新任务状态
                                    update_data = {
                                        "status": task_data.get("status", "unknown"),
                                        "progress": task_data.get("progress", 0),
                                        "updated_at": now_iso
                                    }

                                    # 如果任务已完成、失败或取消，添加结束时间
                                    if task_data.get("status") in ["completed", "failed", "canceled"]:
                                        if "end_time" in task_data and task_data["end_time"]:
                                            update_data["end_time"] = task_data["end_time"]
                                        else:
                                            update_data["end_time"] = now_iso
                                            logger.info(f"为任务{task_id}设置结束时间: {now_iso}")

                                    # 如果有开始时间，更新开始时间
                                    if "start_time" in task_data and task_data["start_time"]:
                                        update_data["start_time"] = task_data["start_time"]
                                    elif task_data.get("status") == "running":
                                        # 如果任务开始运行但没有开始时间，设置开始时间
                                        existing_task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
                                        if existing_task and not existing_task.get("start_time"):
                                            update_data["start_time"] = now_iso
                                            logger.info(f"为任务{task_id}设置开始时间: {now_iso}")

                                    # 如果有日志，添加到日志列表
                                    if "logs" in task_data and task_data["logs"]:
                                        try:
                                            # 过滤重复日志，避免重复插入
                                            new_logs = []
                                            for log in task_data["logs"]:
                                                log_doc = {
                                                    "task_id": task_id,
                                                    "message": log.get("message", ""),
                                                    "level": log.get("level", "info"),
                                                    "timestamp": log.get("timestamp", datetime.now().isoformat()),
                                                    "created_at": datetime.now()
                                                }

                                                # 检查是否已存在相同的日志
                                                existing_log = self.mongo_db.social_task_logs.find_one({
                                                    "task_id": task_id,
                                                    "message": log_doc["message"],
                                                    "timestamp": log_doc["timestamp"]
                                                })

                                                if not existing_log:
                                                    new_logs.append(log_doc)

                                            # 只插入新日志
                                            if new_logs:
                                                self.mongo_db.social_task_logs.insert_many(new_logs)
                                                logger.info(f"已插入任务{task_id}的{len(new_logs)}条新日志")

                                                # 记录错误日志的详细信息
                                                error_logs = [log for log in new_logs if log["level"] == "error"]
                                                if error_logs:
                                                    logger.warning(f"任务{task_id}包含{len(error_logs)}条错误日志")
                                                    for error_log in error_logs:
                                                        logger.error(f"任务{task_id}错误: {error_log['message']}")
                                            else:
                                                logger.debug(f"任务{task_id}没有新日志需要插入")

                                        except Exception as log_error:
                                            logger.error(f"插入任务{task_id}日志失败: {str(log_error)}", exc_info=True)

                                    # 更新任务状态 - 使用同步方式
                                    result = self.mongo_db.social_tasks.update_one(
                                        {"task_id": task_id},
                                        {"$set": update_data}
                                    )

                                    if result.modified_count > 0:
                                        logger.info(f"✅ 已更新任务{task_id}的状态到MongoDB: {task_data.get('status')} (进度: {task_data.get('progress', 0)}%)")

                                        # 🔧 关键修复：如果是子任务，同步更新主任务状态
                                        logger.info(f"🔄 开始同步主任务状态 (如果是子任务): {task_id}")
                                        await self._sync_main_task_status_if_subtask(task_id)

                                        # 🔧 关键：检查是否需要启动下一个子任务
                                        logger.info(f"🚀 检查是否需要启动下一个子任务: {task_id}")
                                        await self._check_and_start_next_subtask(task_id, task_data)
                                        logger.info(f"✅ 子任务检查完成: {task_id}")

                                        # 🔧 重要修复：MongoDB更新成功后，处理任务完成或失败事件
                                        if task_data.get('status') in ['completed', 'failed']:
                                            logger.info(f"🎯 任务{task_id}已{task_data.get('status')}，处理事件")
                                            await self._handle_task_completed(task_id, task_data)
                                    else:
                                        logger.debug(f"任务{task_id}状态无变化，未更新MongoDB")

                                else:
                                    logger.warning("MongoDB数据库连接不可用，无法更新任务状态")
                            except Exception as e:
                                logger.error(f"更新任务{task_id}状态到MongoDB失败: {str(e)}", exc_info=True)
                    except json.JSONDecodeError as e:
                        logger.error(f"解析任务状态JSON失败: {str(e)}")
                        logger.debug(f"原始消息数据: {message.get('data', '')[:100]}...")
                    except Exception as e:
                        logger.error(f"处理任务状态消息异常: {str(e)}", exc_info=True)
                # 处理其他类型的消息
                else:
                    logger.debug(f"收到未处理的频道消息: {channel}")
            else:
                logger.debug(f"收到非消息类型的数据: {message.get('type')}")

        except Exception as e:
            logger.error(f"处理消息异常: {str(e)}", exc_info=True)

    async def _handle_task_completed(self, task_id: str, task_data: Dict[str, Any]) -> None:
        """处理任务完成事件，更新文件发布状态

        Args:
            task_id: 任务ID
            task_data: 任务数据
        """
        try:
            logger.info(f"🎯 处理任务完成事件: {task_id}")

            # 获取任务详细信息
            from app.core.schemas.social_repository import SocialDatabaseService
            db_service = SocialDatabaseService(self.mongo_db)

            # 🔧 修复：查询任务信息 - 使用同步方式
            task = db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                logger.warning(f"任务 {task_id} 不存在，跳过发布状态更新")
                return

            # 检查任务类型，只处理上传任务
            task_type = task.get("task_type", "")
            if task_type not in ["subtask", "single"]:
                logger.debug(f"任务 {task_id} 类型为 {task_type}，不是上传任务，跳过发布状态更新")
                return

            # 获取视频文件路径
            content_path = task.get("content_path", "")
            if not content_path:
                logger.warning(f"任务 {task_id} 没有content_path，跳过发布状态更新")
                return

            # 获取平台和账号信息
            platform_id = task.get("platform_id", "")
            account_id = task.get("account_id", "")

            # 查询平台信息获取平台名称
            platform_name = "youtube"  # 默认值
            if platform_id:
                try:
                    from bson import ObjectId
                    # 🔧 修复：查询平台信息 - 使用同步方式
                    platform_doc = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                    if platform_doc:
                        platform_name = platform_doc.get("name", platform_doc.get("id", "youtube")).lower()
                except Exception as e:
                    logger.warning(f"查询平台信息失败: {str(e)}")

            # 查询账号信息获取账号名称
            account_name = f"账号-{account_id}" if account_id else "未知账号"
            if account_id:
                try:
                    from bson import ObjectId
                    # 🔧 修复：查询账号信息 - 使用同步方式
                    account_doc = db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                    if account_doc:
                        account_name = account_doc.get("display_name", account_doc.get("username", f"账号-{account_id}"))
                except Exception as e:
                    logger.warning(f"查询账号信息失败: {str(e)}")

            logger.info(f"开始为任务 {task_id} 更新文件发布状态: {content_path}")

            # 更新文件发布状态
            success = await self._update_file_publish_status(
                file_path=content_path,
                platform=platform_name,
                account_name=account_name,
                task_id=task_id
            )

            if success:
                logger.info(f"✅ 文件发布状态更新成功: {content_path}")
            else:
                logger.warning(f"⚠️ 文件发布状态更新失败: {content_path}")

            logger.info(f"任务 {task_id} 发布状态更新处理完成")

            # 🔧 关键修复：检查并启动下一个子任务
            logger.info(f"🚀 检查是否需要启动下一个子任务: {task_id}")
            await self._check_and_start_next_subtask(task_id, task_data)
            logger.info(f"✅ 子任务检查完成: {task_id}")

        except Exception as e:
            logger.error(f"处理任务完成事件失败: {task_id}, 错误: {str(e)}")

    async def _update_file_publish_status(
        self,
        file_path: str,
        platform: str,
        account_name: str,
        task_id: str
    ) -> bool:
        """更新单个文件的发布状态

        Args:
            file_path: 文件路径
            platform: 平台名称
            account_name: 账号名称
            task_id: 任务ID

        Returns:
            是否更新成功
        """
        try:
            import hashlib
            import os
            from datetime import datetime
            from app.api.v1.filesystem import update_platform_publish_status, PlatformPublishUpdate

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在，无法更新发布状态: {file_path}")
                return False

            # 计算文件MD5
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            md5_hash = hash_md5.hexdigest()

            # 构建发布状态数据
            platform_data = PlatformPublishUpdate(
                platform=platform,
                is_published=True,
                publish_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                publish_account=account_name,
                video_id="",  # 暂时为空，后续可以从任务数据中获取
                video_url="",  # 暂时为空，后续可以从任务数据中获取
                notes=f"通过任务 {task_id} 自动上传于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # 🔧 暂时跳过文件发布状态更新，避免同步/异步冲突
            # TODO: 创建同步版本的update_platform_publish_status函数
            logger.info(f"⏭️ 跳过文件发布状态更新 (同步/异步冲突): {file_path}")
            success = True  # 暂时返回成功

            if success:
                logger.info(f"文件 {file_path} 发布状态更新成功 (MD5: {md5_hash})")
            else:
                logger.warning(f"文件 {file_path} 发布状态更新失败 (MD5: {md5_hash})")

            return success

        except Exception as e:
            logger.error(f"更新文件发布状态异常: {file_path}, 错误: {str(e)}")
            return False

    async def _sync_main_task_status_if_subtask(self, task_id: str) -> None:
        """如果是子任务，同步更新主任务状态

        Args:
            task_id: 任务ID
        """
        try:
            # 检查当前任务是否是子任务
            current_task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not current_task or current_task.get("task_type") != "subtask":
                return

            parent_task_id = current_task.get("parent_task_id")
            if not parent_task_id:
                return

            logger.info(f"🔄 子任务 {task_id} 状态变化，同步主任务 {parent_task_id} 状态")

            # 调用主任务状态同步逻辑
            await self._sync_main_task_status(parent_task_id)

        except Exception as e:
            logger.error(f"同步主任务状态失败: {str(e)}", exc_info=True)

    async def _sync_main_task_status(self, main_task_id: str) -> None:
        """同步主任务状态

        Args:
            main_task_id: 主任务ID
        """
        try:
            # 获取所有子任务
            subtasks = list(self.mongo_db.social_tasks.find({"parent_task_id": main_task_id}))

            if not subtasks:
                logger.warning(f"主任务 {main_task_id} 没有子任务")
                return

            # 统计子任务状态
            status_count = {}
            for subtask in subtasks:
                status = subtask.get("status", "unknown")
                status_count[status] = status_count.get(status, 0) + 1

            logger.info(f"主任务 {main_task_id} 子任务状态统计: {status_count}")

            # 确定主任务状态
            completed_count = status_count.get("completed", 0)
            failed_count = status_count.get("failed", 0)
            running_count = status_count.get("running", 0)
            pending_count = status_count.get("pending", 0)
            canceled_count = status_count.get("canceled", 0)
            paused_count = status_count.get("paused", 0)

            total_subtasks = len(subtasks)

            # 🔧 修复主任务状态判断逻辑
            new_main_status = None
            if running_count > 0:
                # 有子任务在运行 - 主任务应该是running
                new_main_status = "running"
                logger.info(f"主任务 {main_task_id} 有 {running_count} 个子任务在运行，设置为running")
            elif pending_count > 0 and (completed_count > 0 or failed_count > 0):
                # 🔧 重要修复：如果有pending任务，但也有已完成或失败的任务，说明任务序列已经开始执行
                # 主任务应该是running状态，而不是pending
                new_main_status = "running"
                logger.info(f"主任务 {main_task_id} 任务序列执行中 (pending: {pending_count}, completed: {completed_count}, failed: {failed_count})，设置为running")
            elif pending_count > 0 and failed_count == 0 and canceled_count == 0 and completed_count == 0:
                # 只有当所有任务都是pending状态时，主任务才是pending
                new_main_status = "pending"
                logger.info(f"主任务 {main_task_id} 所有子任务都是pending，设置为pending")
            elif paused_count > 0 and running_count == 0:
                # 有暂停的任务，且没有运行中的
                new_main_status = "paused"
            elif completed_count == total_subtasks:
                # 所有子任务都完成
                new_main_status = "completed"
            elif failed_count > 0 and running_count == 0 and pending_count == 0:
                # 有失败的任务，且没有运行中或等待中的
                new_main_status = "failed"
            elif canceled_count == total_subtasks:
                # 所有子任务都被取消
                new_main_status = "canceled"
            else:
                # 混合状态，保持运行中
                new_main_status = "running"

            # 获取当前主任务状态
            main_task = self.mongo_db.social_tasks.find_one({"task_id": main_task_id})
            if not main_task:
                logger.error(f"主任务 {main_task_id} 不存在")
                return

            current_status = main_task.get("status")

            # 🔧 重要修复：检查主任务是否刚刚重启，避免立即覆盖状态
            task_start_time = main_task.get("start_time")
            recently_restarted = False

            if task_start_time:
                try:
                    start_dt = datetime.fromisoformat(task_start_time.replace('Z', '+00:00'))
                    time_since_start = datetime.now() - start_dt.replace(tzinfo=None)
                    # 如果任务在5分钟内重启，认为是最近重启的
                    recently_restarted = time_since_start.total_seconds() < 300
                    if recently_restarted:
                        logger.info(f"主任务 {main_task_id} 最近重启 ({time_since_start}), 谨慎更新状态")
                except Exception as e:
                    logger.warning(f"解析主任务开始时间失败: {e}")

            # 如果状态需要更新
            if current_status != new_main_status:
                # 🔧 特殊保护：如果主任务刚重启且当前是running，不要轻易改为pending
                if (recently_restarted and current_status == "running" and
                    new_main_status == "pending" and running_count == 0):
                    logger.warning(f"⚠️ 主任务 {main_task_id} 刚重启且当前为running，跳过改为pending的更新")
                    logger.info(f"主任务 {main_task_id} 状态保持: {current_status}")
                    return

                logger.info(f"🔧 更新主任务 {main_task_id} 状态: {current_status} -> {new_main_status}")

                # 🔧 修复时间记录：统一使用ISO格式字符串
                now_iso = datetime.now().isoformat()

                update_data = {
                    "status": new_main_status,
                    "updated_at": now_iso,
                    "completed_subtasks": completed_count,
                    "progress": int((completed_count / total_subtasks) * 100) if total_subtasks > 0 else 0
                }

                # 如果主任务变为完成或失败，设置结束时间
                if new_main_status in ["completed", "failed", "canceled"]:
                    update_data["end_time"] = now_iso
                    logger.info(f"主任务{main_task_id}设置结束时间: {now_iso}")
                elif new_main_status == "running" and not main_task.get("start_time"):
                    # 如果主任务开始运行但没有开始时间，设置开始时间
                    update_data["start_time"] = now_iso
                    logger.info(f"主任务{main_task_id}设置开始时间: {now_iso}")

                # 更新数据库
                self.mongo_db.social_tasks.update_one(
                    {"task_id": main_task_id},
                    {"$set": update_data}
                )

                logger.info(f"✅ 主任务 {main_task_id} 状态同步完成: {new_main_status}")
            else:
                logger.info(f"主任务 {main_task_id} 状态无需更新: {current_status}")

        except Exception as e:
            logger.error(f"同步主任务状态失败: {str(e)}", exc_info=True)

    async def _check_and_start_next_subtask(self, task_id: str, task_data: Dict[str, Any]) -> None:
        """检查并启动下一个子任务

        Args:
            task_id: 当前任务ID
            task_data: 当前任务数据
        """
        try:
            # 🔧 详细日志：开始检查
            task_status = task_data.get("status")
            logger.info(f"🔍 开始检查子任务启动逻辑: task_id={task_id}, status={task_status}")

            # 🔧 强制输出到控制台
            print(f"🔍 _check_and_start_next_subtask 被调用: task_id={task_id}, status={task_status}")

            # 只有当子任务完成或失败时才检查
            if task_status not in ["completed", "failed"]:
                logger.info(f"⏭️ 任务{task_id}状态为{task_status}，不需要启动下一个子任务")
                return

            # 检查当前任务是否是子任务
            current_task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not current_task:
                logger.warning(f"❌ 找不到任务{task_id}")
                return

            task_type = current_task.get("task_type")
            if task_type != "subtask":
                logger.info(f"⏭️ 任务{task_id}类型为{task_type}，不是子任务，跳过")
                return

            parent_task_id = current_task.get("parent_task_id")
            if not parent_task_id:
                logger.warning(f"❌ 子任务{task_id}没有parent_task_id")
                return

            logger.info(f"🎯 子任务{task_id}已{task_status}，开始检查是否需要启动下一个子任务，主任务ID: {parent_task_id}")

            # 🔧 优雅的分布式锁机制：使用带重试的分布式锁
            success = await self._acquire_distributed_lock_with_retry(
                f"subtask_start_lock:{parent_task_id}",
                self._process_next_subtask_logic,
                parent_task_id,
                max_retries=3,
                retry_delay=1.0
            )

            if not success:
                logger.warning(f"获取分布式锁失败，跳过子任务启动处理: {parent_task_id}")

        except Exception as e:
            logger.error(f"检查并启动下一个子任务异常: {str(e)}", exc_info=True)

    async def _acquire_distributed_lock_with_retry(self, lock_key: str, callback_func, *args, max_retries: int = 3, retry_delay: float = 1.0) -> bool:
        """获取分布式锁并执行回调函数，支持重试机制

        Args:
            lock_key: 锁的键名
            callback_func: 获取锁后要执行的回调函数
            *args: 回调函数的参数
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）

        Returns:
            bool: 是否成功获取锁并执行
        """
        for attempt in range(max_retries + 1):
            try:
                # 尝试获取分布式锁
                lock_acquired = await self._try_acquire_distributed_lock(lock_key)

                if lock_acquired:
                    try:
                        # 执行回调函数
                        await callback_func(*args)
                        return True
                    finally:
                        # 确保释放锁
                        await self._release_distributed_lock(lock_key)
                else:
                    if attempt < max_retries:
                        logger.info(f"锁 {lock_key} 被占用，{retry_delay}秒后重试 (尝试 {attempt + 1}/{max_retries + 1})")
                        await asyncio.sleep(retry_delay)
                    else:
                        logger.warning(f"经过 {max_retries} 次重试仍无法获取锁: {lock_key}")
                        return False

            except Exception as e:
                logger.error(f"获取分布式锁异常 (尝试 {attempt + 1}): {str(e)}")
                if attempt < max_retries:
                    await asyncio.sleep(retry_delay)
                else:
                    return False

        return False

    async def _try_acquire_distributed_lock(self, lock_key: str, expire_time: int = 60) -> bool:
        """尝试获取分布式锁

        Args:
            lock_key: 锁的键名
            expire_time: 锁的过期时间（秒）

        Returns:
            bool: 是否成功获取锁
        """
        try:
            # 生成唯一的锁值，包含服务实例信息
            import socket
            import os
            hostname = socket.gethostname()
            pid = os.getpid()
            lock_value = f"backend_{hostname}_{pid}_{int(time.time())}"

            # 使用 SET NX EX 原子操作获取锁
            result = await self.redis_client.set(lock_key, lock_value, nx=True, ex=expire_time)

            if result:
                logger.info(f"✅ 成功获取分布式锁: {lock_key} (值: {lock_value})")
                # 将锁值存储起来，用于释放时验证
                self._current_locks = getattr(self, '_current_locks', {})
                self._current_locks[lock_key] = lock_value
                return True
            else:
                # 检查锁的持有者信息
                current_lock_value = await self.redis_client.get(lock_key)
                logger.debug(f"❌ 锁已被占用: {lock_key} (持有者: {current_lock_value})")
                return False

        except Exception as e:
            logger.error(f"尝试获取分布式锁失败: {str(e)}")
            return False

    async def _release_distributed_lock(self, lock_key: str) -> bool:
        """释放分布式锁

        Args:
            lock_key: 锁的键名

        Returns:
            bool: 是否成功释放锁
        """
        try:
            # 获取当前锁值
            current_locks = getattr(self, '_current_locks', {})
            expected_lock_value = current_locks.get(lock_key)

            if not expected_lock_value:
                logger.warning(f"尝试释放未持有的锁: {lock_key}")
                return False

            # 使用 Lua 脚本确保原子性：只有锁值匹配才删除
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """

            result = await self.redis_client.eval(lua_script, 1, lock_key, expected_lock_value)

            if result == 1:
                logger.info(f"✅ 成功释放分布式锁: {lock_key}")
                # 从本地记录中移除
                current_locks.pop(lock_key, None)
                return True
            else:
                logger.warning(f"❌ 锁已被其他实例持有或已过期: {lock_key}")
                return False

        except Exception as e:
            logger.error(f"释放分布式锁失败: {str(e)}")
            return False

    async def _process_next_subtask_logic(self, parent_task_id: str) -> None:
        """处理下一个子任务的启动逻辑（在分布式锁保护下执行）

        Args:
            parent_task_id: 主任务ID
        """
        try:
            logger.info(f"🔒 在分布式锁保护下处理主任务 {parent_task_id} 的下一个子任务")

            # 查找主任务
            main_task = self.mongo_db.social_tasks.find_one({"task_id": parent_task_id})
            if not main_task:
                logger.warning(f"找不到主任务: {parent_task_id}")
                return

            # 查找所有子任务
            all_subtasks = list(self.mongo_db.social_tasks.find({
                "parent_task_id": parent_task_id,
                "task_type": "subtask"
            }).sort("subtask_index", 1))

            if not all_subtasks:
                logger.warning(f"主任务{parent_task_id}没有子任务")
                return

            logger.info(f"主任务{parent_task_id}共有{len(all_subtasks)}个子任务")

            # 🔧 重要：检查是否有正在运行的子任务
            running_subtasks = [task for task in all_subtasks if task.get("status") == "running"]
            if running_subtasks:
                logger.info(f"主任务{parent_task_id}有{len(running_subtasks)}个正在运行的子任务，跳过启动")
                for running_task in running_subtasks:
                    logger.info(f"  - 正在运行的子任务: {running_task['task_id']}")
                return

            # 查找下一个待执行的子任务
            next_subtask = None
            for subtask in all_subtasks:
                if subtask.get("status") == "pending":
                    next_subtask = subtask
                    break

            if next_subtask:
                next_task_id = next_subtask["task_id"]
                logger.info(f"🚀 找到下一个待执行的子任务: {next_task_id}")

                # 启动下一个子任务
                await self._start_subtask_with_device_allocation(next_task_id)
                logger.info(f"✅ 子任务{next_task_id}启动处理完成")
            else:
                # 检查所有子任务是否都已完成
                completed_count = len([task for task in all_subtasks if task.get("status") == "completed"])
                failed_count = len([task for task in all_subtasks if task.get("status") == "failed"])
                total_count = len(all_subtasks)

                logger.info(f"主任务{parent_task_id}子任务状态统计: 完成{completed_count}, 失败{failed_count}, 总计{total_count}")

                if completed_count + failed_count == total_count:
                    # 所有子任务都已完成，更新主任务状态
                    if failed_count == 0:
                        new_status = "completed"
                        logger.info(f"🎉 主任务{parent_task_id}所有子任务都已完成，更新主任务状态为completed")
                    else:
                        new_status = "failed"
                        logger.info(f"❌ 主任务{parent_task_id}有{failed_count}个子任务失败，更新主任务状态为failed")

                    # 更新主任务状态
                    self.mongo_db.social_tasks.update_one(
                        {"task_id": parent_task_id},
                        {"$set": {
                            "status": new_status,
                            "end_time": datetime.now().isoformat(),
                            "updated_at": datetime.now(),
                            "progress": 100
                        }}
                    )

                    # 发布主任务状态更新到Redis
                    await self._publish_task_status_to_redis(parent_task_id, new_status)
                    logger.info(f"✅ 主任务{parent_task_id}状态已更新为{new_status}")
                else:
                    logger.info(f"主任务{parent_task_id}还有子任务未完成，等待中...")

        except Exception as e:
            logger.error(f"处理下一个子任务逻辑失败: {str(e)}", exc_info=True)

    async def _publish_task_status_to_redis(self, task_id: str, status: str) -> None:
        """发布任务状态到Redis

        Args:
            task_id: 任务ID
            status: 任务状态
        """
        try:
            # 获取任务详细信息
            task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not task:
                logger.warning(f"找不到任务 {task_id}，无法发布状态")
                return

            # 构建状态数据
            status_data = {
                "task_id": task_id,
                "status": status,
                "progress": task.get("progress", 0),
                "updated_at": datetime.now().isoformat()
            }

            # 发布到Redis
            channel = f"task:{task_id}:status"
            await self.redis_client.publish(channel, json.dumps(status_data))
            logger.info(f"📡 已发布任务状态到Redis: {task_id} -> {status}")

        except Exception as e:
            logger.error(f"发布任务状态到Redis失败: {str(e)}")

    async def _get_task_owner(self, task_id: str) -> str:
        """获取任务的归属Backend实例ID

        Args:
            task_id: 任务ID

        Returns:
            str: Backend实例ID，如果未找到则返回None
        """
        try:
            # 首先从Redis缓存中查找
            cache_key = f"task:{task_id}:owner"
            cached_owner = await self.redis_client.get(cache_key)
            if cached_owner:
                owner = cached_owner.decode('utf-8') if isinstance(cached_owner, bytes) else cached_owner
                logger.info(f"📋 从Redis缓存获取任务{task_id}归属: {owner}")
                return owner

            # 如果缓存中没有，从MongoDB查找
            task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if task:
                logger.info(f"📋 从MongoDB查询任务{task_id}: backend_instance_id={task.get('backend_instance_id', '未设置')}")
                if "backend_instance_id" in task and task["backend_instance_id"]:
                    owner = task["backend_instance_id"]
                    # 缓存到Redis，设置1小时过期
                    await self.redis_client.setex(cache_key, 3600, owner)
                    logger.info(f"📋 从MongoDB获取任务{task_id}归属: {owner}，已缓存到Redis")
                    return owner
                else:
                    logger.info(f"📋 任务{task_id}在MongoDB中无backend_instance_id字段或为空")
            else:
                logger.info(f"📋 任务{task_id}在MongoDB中不存在")

            # 🔧 临时修复：如果任务没有归属信息，且任务正在运行，则设置为当前Backend实例
            if task and task.get('status') in ['running', 'pending']:
                logger.info(f"🔧 检测到运行中任务{task_id}无归属信息，设置为当前Backend实例: {self.backend_instance_id}")
                try:
                    # 更新MongoDB
                    self.mongo_db.social_tasks.update_one(
                        {"task_id": task_id},
                        {"$set": {
                            "backend_instance_id": self.backend_instance_id,
                            "started_by": self.backend_instance_id
                        }}
                    )
                    # 缓存到Redis
                    await self.redis_client.setex(cache_key, 3600, self.backend_instance_id)
                    logger.info(f"✅ 已为运行中任务{task_id}设置归属: {self.backend_instance_id}")
                    return self.backend_instance_id
                except Exception as update_error:
                    logger.error(f"设置任务{task_id}归属失败: {str(update_error)}")

            # 如果任务没有归属信息，允许当前实例处理
            logger.info(f"📋 任务{task_id}无归属信息，返回None（允许当前实例处理）")
            return None

        except Exception as e:
            logger.error(f"获取任务{task_id}归属失败: {str(e)}")
            return None

    async def _cleanup_expired_locks(self) -> None:
        """清理过期的分布式锁（可选的维护任务）"""
        try:
            current_locks = getattr(self, '_current_locks', {})
            if not current_locks:
                return

            # 检查每个锁是否仍然存在
            for lock_key in list(current_locks.keys()):
                try:
                    lock_value = await self.redis_client.get(lock_key)
                    if not lock_value or lock_value != current_locks[lock_key]:
                        # 锁已过期或被其他实例持有
                        current_locks.pop(lock_key, None)
                        logger.debug(f"清理过期锁记录: {lock_key}")
                except Exception as e:
                    logger.warning(f"检查锁状态失败: {lock_key}, {e}")

        except Exception as e:
            logger.error(f"清理过期锁失败: {str(e)}")

    async def _start_subtask(self, task_id: str) -> None:
        """启动子任务

        Args:
            task_id: 子任务ID
        """
        try:
            # 获取子任务信息
            subtask = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not subtask:
                logger.error(f"找不到子任务: {task_id}")
                return

            # 检查子任务是否有设备ID，如果没有则从主任务或其他子任务获取
            device_id = subtask.get("device_id")
            if not device_id:
                logger.warning(f"子任务{task_id}缺少设备ID，尝试从主任务获取")

                # 从主任务获取设备ID
                parent_task_id = subtask.get("parent_task_id")
                if parent_task_id:
                    # 查找同一主任务下已经有设备ID的子任务
                    sibling_task = self.mongo_db.social_tasks.find_one({
                        "parent_task_id": parent_task_id,
                        "task_type": "subtask",
                        "device_id": {"$exists": True, "$ne": None}
                    })

                    if sibling_task:
                        device_id = sibling_task.get("device_id")
                        logger.info(f"从兄弟任务获取设备ID: {device_id}")
                    else:
                        # 从主任务获取设备ID
                        main_task = self.mongo_db.social_tasks.find_one({"task_id": parent_task_id})
                        if main_task:
                            device_id = main_task.get("device_id")
                            logger.info(f"从主任务获取设备ID: {device_id}")

                if not device_id:
                    logger.error(f"无法为子任务{task_id}获取设备ID")
                    return

            # 更新子任务状态为运行中，并确保有设备ID
            update_data = {
                "status": "running",
                "start_time": datetime.now().isoformat(),
                "updated_at": datetime.now()
            }

            # 如果子任务原来没有设备ID，添加设备ID
            if not subtask.get("device_id"):
                update_data["device_id"] = device_id

            self.mongo_db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )

            # 重新获取更新后的子任务信息
            subtask = self.mongo_db.social_tasks.find_one({"task_id": task_id})

            logger.info(f"已更新子任务{task_id}状态为运行中，设备ID: {device_id}")

            # 🔧 修复：使用Backend API启动任务，确保使用相同的YouTube检测逻辑
            success = await self._start_subtask_via_backend(task_id)

            # 🔧 重要：如果启动失败，回滚任务状态
            if not success:
                logger.error(f"子任务{task_id}启动失败，回滚状态")
                self.mongo_db.social_tasks.update_one(
                    {"task_id": task_id},
                    {"$set": {
                        "status": "failed",
                        "end_time": datetime.now().isoformat(),
                        "updated_at": datetime.now(),
                        "error_message": "Core服务启动失败"
                    }}
                )
            else:
                # 🔧 启动成功后，创建延迟验证任务
                logger.info(f"子任务{task_id}启动成功，将在30秒后验证执行状态")
                asyncio.create_task(self._verify_subtask_execution(task_id, 30))

        except Exception as e:
            logger.error(f"启动子任务{task_id}失败: {str(e)}", exc_info=True)
            # 🔧 重要：异常时也要回滚状态
            try:
                self.mongo_db.social_tasks.update_one(
                    {"task_id": task_id},
                    {"$set": {
                        "status": "failed",
                        "end_time": datetime.now().isoformat(),
                        "updated_at": datetime.now(),
                        "error_message": f"启动异常: {str(e)}"
                    }}
                )
            except Exception as db_error:
                logger.error(f"回滚任务状态失败: {str(db_error)}")

    async def _notify_core_service(self, task_id: str, task_data: Dict[str, Any]) -> None:
        """通知Core服务启动任务

        Args:
            task_id: 任务ID
            task_data: 任务数据
        """
        try:
            import aiohttp

            # Core服务的API地址（需要从配置获取）
            core_url = "http://localhost:8001"  # 默认Core服务地址

            # 🔧 修复：如果是子任务且没有metadata，从父任务继承
            task_metadata = task_data.get("metadata", {})
            if task_data.get("task_type") == "subtask" and task_data.get("parent_task_id"):
                # 检查是否需要从父任务继承metadata
                needs_inheritance = (
                    not task_metadata or  # metadata为空
                    not task_metadata.get("contentType") or  # 缺少contentType
                    not task_metadata.get("titleTemplate")  # 缺少titleTemplate
                )

                if needs_inheritance:
                    parent_task = self.mongo_db.social_tasks.find_one({"task_id": task_data.get("parent_task_id")})
                    if parent_task and parent_task.get("metadata"):
                        task_metadata = parent_task.get("metadata", {})
                        logger.info(f"🔧 子任务{task_id}从父任务继承metadata: {task_metadata}")
                        logger.info(f"🔧 继承的contentType: {task_metadata.get('contentType')}")

            # 构造请求数据
            request_data = {
                "task_id": task_id,
                "platform_id": task_data.get("platform_id"),
                "account_id": task_data.get("account_id"),
                "device_id": task_data.get("device_id"),
                "content_path": task_data.get("content_path"),
                "metadata": task_metadata,  # 🔧 使用继承后的metadata
                "task_type": task_data.get("task_type", "subtask"),
                "subtask_index": task_data.get("subtask_index"),
                "video_file": task_data.get("video_file")
            }

            logger.info(f"通知Core服务启动任务{task_id}")

            async with aiohttp.ClientSession() as session:
                # 先创建任务
                async with session.post(
                    f"{core_url}/api/tasks",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        logger.info(f"Core服务任务{task_id}创建成功")

                        # 然后启动任务
                        async with session.post(
                            f"{core_url}/api/tasks/{task_id}/start",
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as start_response:
                            if start_response.status == 200:
                                logger.info(f"Core服务任务{task_id}启动成功")
                            else:
                                logger.error(f"Core服务任务{task_id}启动失败: {start_response.status}")
                    else:
                        logger.error(f"Core服务任务{task_id}创建失败: {response.status}")

        except Exception as e:
            logger.error(f"通知Core服务启动任务{task_id}失败: {str(e)}", exc_info=True)

    async def _start_subtask_via_backend(self, task_id: str) -> bool:
        """通过Backend API启动子任务（复用设备分配逻辑）

        Args:
            task_id: 子任务ID

        Returns:
            bool: 是否启动成功
        """
        try:
            import aiohttp

            # Backend API地址
            backend_url = "http://localhost:8000"  # 默认Backend地址

            logger.info(f"通过Backend API启动子任务: {task_id}")

            async with aiohttp.ClientSession() as session:
                # 调用Backend的启动任务API
                async with session.post(
                    f"{backend_url}/api/social/tasks/{task_id}/start",
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response_text = await response.text()
                    logger.info(f"Backend启动任务响应: {response.status} - {response_text}")

                    if response.status == 200:
                        logger.info(f"通过Backend成功启动子任务: {task_id}")
                        return True
                    else:
                        logger.error(f"通过Backend启动子任务失败: {task_id} - {response.status}")
                        return False

        except Exception as e:
            logger.error(f"通过Backend启动子任务{task_id}失败: {str(e)}", exc_info=True)
            return False

    async def _verify_subtask_execution(self, task_id: str, delay_seconds: int = 30) -> None:
        """验证子任务是否真正开始执行

        Args:
            task_id: 子任务ID
            delay_seconds: 延迟验证的秒数
        """
        try:
            # 等待指定时间
            await asyncio.sleep(delay_seconds)

            logger.info(f"🔍 验证子任务{task_id}执行状态...")

            # 检查任务当前状态
            subtask = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not subtask:
                logger.warning(f"验证时找不到子任务: {task_id}")
                return

            current_status = subtask.get("status")
            progress = subtask.get("progress", 0)
            start_time = subtask.get("start_time")

            logger.info(f"子任务{task_id}当前状态: {current_status}, 进度: {progress}%")

            # 检查是否有进展
            has_progress = False

            # 1. 检查进度是否有变化
            if progress > 0:
                has_progress = True
                logger.info(f"✅ 子任务{task_id}有进度更新: {progress}%")

            # 2. 检查Redis中的状态
            if not has_progress:
                redis_key = f"task:{task_id}:latest"
                redis_status = await self.redis_client.get(redis_key)
                if redis_status:
                    try:
                        import json
                        redis_data = json.loads(redis_status)
                        redis_progress = redis_data.get("progress", 0)
                        if redis_progress > 0:
                            has_progress = True
                            logger.info(f"✅ 子任务{task_id}在Redis中有进度: {redis_progress}%")
                    except json.JSONDecodeError:
                        pass

            # 3. 检查任务日志
            if not has_progress:
                # 查找最近的日志
                recent_logs = list(self.mongo_db.social_task_logs.find({
                    "task_id": task_id,
                    "created_at": {"$gte": start_time}
                }).sort("created_at", -1).limit(5))

                if recent_logs:
                    has_progress = True
                    logger.info(f"✅ 子任务{task_id}有日志更新，共{len(recent_logs)}条")

            # 如果没有任何进展，认为任务可能卡住了
            if not has_progress and current_status == "running":
                logger.warning(f"⚠️ 子任务{task_id}可能卡住了，{delay_seconds}秒内没有任何进展")

                # 可以选择重启任务或标记为失败
                # 这里先记录警告，不自动处理
                self.mongo_db.social_task_logs.insert_one({
                    "task_id": task_id,
                    "level": "warning",
                    "message": f"任务可能卡住，{delay_seconds}秒内没有进展",
                    "created_at": datetime.now().isoformat()
                })
            else:
                logger.info(f"✅ 子任务{task_id}执行正常")

        except Exception as e:
            logger.error(f"验证子任务{task_id}执行状态失败: {str(e)}", exc_info=True)

    async def _start_subtask_via_task_api(self, task_id: str) -> None:
        """通过Task API启动子任务（与手动重启使用相同逻辑）

        Args:
            task_id: 子任务ID
        """
        try:
            import aiohttp

            # Backend Task API地址
            backend_url = "http://localhost:8000"  # 默认Backend地址

            logger.info(f"通过Task API启动子任务: {task_id}")

            async with aiohttp.ClientSession() as session:
                # 调用Backend的Task API启动任务
                async with session.post(
                    f"{backend_url}/api/tasks/{task_id}/start",
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response_text = await response.text()
                    logger.info(f"Task API启动任务响应: {response.status} - {response_text}")

                    if response.status == 200:
                        logger.info(f"通过Task API成功启动子任务: {task_id}")
                    else:
                        logger.error(f"通过Task API启动子任务失败: {task_id} - {response.status}")

        except Exception as e:
            logger.error(f"通过Task API启动子任务{task_id}失败: {str(e)}", exc_info=True)

    async def _start_subtask_via_backend_api(self, task_id: str) -> None:
        """通过Backend API启动子任务（确保使用完整的排队检查逻辑）

        Args:
            task_id: 子任务ID
        """
        try:
            logger.info(f"🔄 Redis同步服务启动子任务: {task_id}")

            # 🔧 重要修复：使用API启动逻辑，确保排队检查
            # 不再直接设置状态为running，而是调用API启动逻辑

            # 导入API启动函数
            from app.api.task import start_task_internal
            from app.core.schemas.social_repository import SocialDatabaseService

            # 创建数据库服务
            db_service = SocialDatabaseService(self.mongo_db)

            logger.info(f"📞 调用API内部启动逻辑: {task_id}")

            # 调用API内部启动逻辑，这会包含排队检查
            result = await start_task_internal(task_id, db_service)

            logger.info(f"📋 API启动结果: {json.dumps(result, ensure_ascii=False)}")

            if result.get("success"):
                if result.get("details", {}).get("queued"):
                    logger.info(f"⏳ 子任务 {task_id} 已进入等待队列: {result.get('message')}")
                else:
                    logger.info(f"✅ 子任务 {task_id} 启动成功: {result.get('message')}")
            else:
                logger.error(f"❌ 子任务 {task_id} 启动失败: {result.get('error')}")

                # 🔧 关键修复：子任务启动失败时，继续尝试下一个子任务
                logger.info(f"🔄 子任务 {task_id} 启动失败，尝试启动下一个子任务")

                # 获取主任务ID
                try:
                    failed_task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
                    if failed_task and failed_task.get("parent_task_id"):
                        parent_task_id = failed_task.get("parent_task_id")

                        # 将失败的任务标记为failed状态
                        self.mongo_db.social_tasks.update_one(
                            {"task_id": task_id},
                            {"$set": {
                                "status": "failed",
                                "updated_at": datetime.datetime.now().isoformat(),
                                "end_time": datetime.datetime.now().isoformat()
                            }}
                        )

                        # 递归调用检查下一个子任务
                        logger.info(f"🔄 递归检查主任务 {parent_task_id} 的下一个子任务")
                        await self._check_and_start_next_subtask(task_id, {"status": "failed"})

                except Exception as retry_error:
                    logger.error(f"❌ 尝试启动下一个子任务失败: {str(retry_error)}")

        except Exception as e:
            logger.error(f"❌ Redis同步服务启动子任务{task_id}失败: {str(e)}", exc_info=True)

    async def _start_subtask_with_device_allocation(self, task_id: str) -> None:
        """启动子任务并分配设备

        Args:
            task_id: 子任务ID
        """
        try:
            # 获取子任务信息
            subtask = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not subtask:
                logger.error(f"找不到子任务: {task_id}")
                return

            # 分配设备ID
            device_id = await self._allocate_device_for_task(subtask)
            if not device_id:
                logger.error(f"无法为子任务{task_id}分配设备")
                return

            # 更新子任务状态和设备ID
            update_data = {
                "status": "running",
                "start_time": datetime.now().isoformat(),
                "updated_at": datetime.now(),
                "device_id": device_id
            }

            self.mongo_db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )

            # 重新获取更新后的子任务信息
            subtask = self.mongo_db.social_tasks.find_one({"task_id": task_id})

            logger.info(f"已为子任务{task_id}分配设备{device_id}并更新状态为运行中")

            # 🔧 修复：使用task API启动任务，确保与手动重启使用相同逻辑
            await self._start_subtask_via_task_api(task_id)

        except Exception as e:
            logger.error(f"启动子任务{task_id}失败: {str(e)}", exc_info=True)

    async def _allocate_device_for_task(self, task: Dict[str, Any]) -> str:
        """为任务分配设备

        Args:
            task: 任务数据

        Returns:
            设备ID，如果分配失败返回None
        """
        try:
            # 如果任务已经有设备ID，直接返回
            if task.get("device_id"):
                return task["device_id"]

            account_id = task.get("account_id")
            platform_id = task.get("platform_id")
            task_id = task.get("task_id")

            logger.info(f"为任务{task_id}分配设备，账号ID: {account_id}, 平台ID: {platform_id}")

            # 🔧 重要修复：使用设备分配锁防止并发分配冲突
            device_allocation_lock = f"device_allocation_lock:{task_id}"

            try:
                # 设置分配锁，防止并发分配
                lock_acquired = await self.redis_client.set(device_allocation_lock, "locked", ex=30, nx=True)
                if not lock_acquired:
                    logger.warning(f"任务{task_id}设备分配锁已存在，等待其他分配完成")
                    # 等待一段时间后重试
                    await asyncio.sleep(2)
                    return await self._allocate_device_for_task(task)

                # 1. 尝试从同一主任务的其他子任务获取设备ID（仅用于串行执行的子任务）
                parent_task_id = task.get("parent_task_id")
                if parent_task_id:
                    # 🔧 修复：检查是否有正在运行的兄弟任务使用设备
                    running_sibling = self.mongo_db.social_tasks.find_one({
                        "parent_task_id": parent_task_id,
                        "task_type": "subtask",
                        "status": "running",
                        "device_id": {"$exists": True, "$ne": None}
                    })

                    if running_sibling:
                        # 如果有正在运行的兄弟任务，使用相同设备（串行执行）
                        device_id = running_sibling.get("device_id")
                        logger.info(f"从正在运行的兄弟任务获取设备ID: {device_id} (串行执行)")
                        return device_id
                    else:
                        # 如果没有正在运行的兄弟任务，尝试从已完成的任务获取设备ID
                        completed_sibling = self.mongo_db.social_tasks.find_one({
                            "parent_task_id": parent_task_id,
                            "task_type": "subtask",
                            "device_id": {"$exists": True, "$ne": None}
                        })

                        if completed_sibling:
                            device_id = completed_sibling.get("device_id")
                            # 🔧 重要：检查设备是否被其他任务占用
                            if await self._is_device_available(device_id):
                                logger.info(f"从已完成的兄弟任务获取可用设备ID: {device_id}")
                                return device_id
                            else:
                                logger.warning(f"设备{device_id}被其他任务占用，需要重新分配")

                # 2. 尝试根据账号和平台查找设备映射
                if account_id and platform_id:
                    mapping = self.mongo_db.device_account_mappings.find_one({
                        "account_id": account_id,
                        "platform_id": platform_id,
                        "status": "active"
                    })

                    if mapping and "device_id" in mapping:
                        device_id = str(mapping["device_id"])
                        # 🔧 重要：检查映射的设备是否可用
                        if await self._is_device_available(device_id):
                            logger.info(f"根据账号{account_id}和平台{platform_id}找到可用设备映射: {device_id}")
                            await self._mark_device_occupied(device_id, task_id)
                            return device_id
                        else:
                            logger.warning(f"映射的设备{device_id}不可用，尝试查找其他设备")

                    # 如果没有找到特定平台的映射，尝试只根据账号查找
                    mapping = self.mongo_db.device_account_mappings.find_one({
                        "account_id": account_id,
                        "status": "active"
                    })

                    if mapping and "device_id" in mapping:
                        device_id = str(mapping["device_id"])
                        # 🔧 重要：检查映射的设备是否可用
                        if await self._is_device_available(device_id):
                            logger.info(f"根据账号{account_id}找到可用设备映射: {device_id}")
                            await self._mark_device_occupied(device_id, task_id)
                            return device_id
                        else:
                            logger.warning(f"映射的设备{device_id}不可用")

                # 3. 如果没有找到可用的映射设备，尝试分配其他可用设备
                logger.info("尝试分配其他可用设备")
                device_id = await self._find_available_device()

                if device_id:
                    logger.info(f"分配到可用设备: {device_id}")
                    await self._mark_device_occupied(device_id, task_id)
                    return device_id
                else:
                    logger.error("没有可用的设备")
                    return None

            finally:
                # 释放分配锁
                try:
                    await self.redis_client.delete(device_allocation_lock)
                except Exception as e:
                    logger.warning(f"释放设备分配锁失败: {e}")

        except Exception as e:
            logger.error(f"分配设备失败: {str(e)}", exc_info=True)
            return None

    async def _update_device_in_db(self, device_id: str, status: str, timestamp: int, core_id: str = "default") -> None:
        """更新设备状态到数据库

        Args:
            device_id: 设备ID
            status: 设备状态
            timestamp: 时间戳
            core_id: Core服务ID，默认为"default"
        """
        try:
            # 首先更新基本状态
            update_data = {
                "status": status,
                "updated_at": datetime.fromtimestamp(timestamp),
                "core_id": core_id  # 添加Core服务标识
            }

            # 尝试从Redis获取完整的设备信息
            try:
                # 尝试使用新的键名格式（带有Core ID）
                state_json_new = await self.redis_client.get(f"device:{core_id}:{device_id}:state")
                last_change_json_new = await self.redis_client.get(f"device:{core_id}:{device_id}:last_change")

                # 尝试使用旧的键名格式（不带Core ID）
                state_json_old = await self.redis_client.get(f"device:{device_id}:state")
                last_change_json_old = await self.redis_client.get(f"device:{device_id}:last_change")

                # 使用新格式的数据，如果没有则使用旧格式的数据
                state_json = state_json_new if state_json_new else state_json_old
                last_change_json = last_change_json_new if last_change_json_new else last_change_json_old

                if state_json:
                    state = json.loads(state_json)

                    # 添加设备名称和类型
                    if "name" in state:
                        update_data["name"] = state.get("name")

                    # 设备类型 - 注意MongoDB中字段名是"type"
                    if "device_type" in state:
                        update_data["type"] = state.get("device_type")

                    # 添加硬件信息
                    if "hardware_info" in state:
                        hardware_info = state.get("hardware_info", {})
                        # 获取分辨率信息
                        width = hardware_info.get('width', 1080)
                        height = hardware_info.get('height', 1920)

                        # 如果hardware_info中没有分辨率，尝试从display_info获取
                        if "display_info" in state:
                            display_info = state.get("display_info", {})
                            if not width and "width" in display_info:
                                width = display_info.get("width", 1080)
                            if not height and "height" in display_info:
                                height = display_info.get("height", 1920)

                        update_data["config"] = {
                            "resolution": f"{width}x{height}",
                            "cpu_cores": hardware_info.get("cpu_cores", 2),
                            "memory": hardware_info.get("memory_size", 2048),
                            "adb_port": hardware_info.get("adb_port", 5555)
                        }

                    # 添加窗口信息
                    if "window_info" in state:
                        update_data["window_info"] = state.get("window_info", {})

                    # 添加进程信息
                    if "process_info" in state:
                        update_data["process_info"] = state.get("process_info", {})

                    # 添加CPU和内存使用率
                    if "cpu_usage" in state:
                        update_data["cpu_usage"] = state.get("cpu_usage")

                    if "memory_usage" in state:
                        update_data["memory_usage"] = state.get("memory_usage")

                    if "network_status" in state:
                        update_data["network_status"] = state.get("network_status")

                    # 如果有最后一次变更信息，添加到设备数据中
                    if last_change_json:
                        try:
                            last_change = json.loads(last_change_json)
                            update_data["last_change"] = {
                                "old_status": last_change.get("old_status", "unknown"),
                                "new_status": last_change.get("new_status", "unknown"),
                                "timestamp": last_change.get("timestamp", int(time.time()))
                            }
                        except Exception:
                            pass

                    logger.debug(f"从Redis获取到设备{device_id}（Core: {core_id}）的完整信息")
                else:
                    logger.warning(f"设备{device_id}（Core: {core_id}）在Redis中没有状态数据")
            except Exception as e:
                logger.warning(f"从Redis获取设备{device_id}（Core: {core_id}）完整信息失败: {str(e)}")

            # 确保创建时间字段存在
            try:
                # 使用同步方式查询MongoDB
                existing_device = self.mongo_db.devices.find_one({"_id": device_id})
                if not existing_device:
                    update_data["created_at"] = datetime.now()
                elif "created_at" not in existing_device:
                    # 如果设备存在但没有创建时间，添加一个
                    update_data["created_at"] = datetime.now()

                # 记录更新前的字段数量
                before_fields = 0
                if existing_device:
                    before_fields = len(existing_device)
            except Exception as e:
                logger.error(f"查询设备{device_id}异常: {str(e)}")
                # 如果查询失败，默认添加创建时间
                update_data["created_at"] = datetime.now()
                before_fields = 0

            # 记录更新数据的详细内容
            logger.info(f"准备更新设备{device_id}的数据，字段数: {len(update_data)}")
            logger.info(f"更新数据内容: {update_data}")

            # 更新设备状态 - 使用同步方式
            try:
                result = self.mongo_db.devices.update_one(
                    {"_id": device_id},
                    {"$set": update_data},
                    upsert=True  # 如果设备不存在则创建
                )

                logger.info(f"MongoDB更新结果: matched={result.matched_count}, modified={result.modified_count}, upserted={result.upserted_id}")

                if result.modified_count > 0 or result.upserted_id:
                    logger.info(f"设备状态已更新: {device_id}, 状态: {status}, 字段数: {len(update_data)}, 之前字段数: {before_fields}")

                    # 验证更新是否成功
                    try:
                        updated_device = self.mongo_db.devices.find_one({"_id": device_id})
                        if updated_device:
                            logger.info(f"更新后的设备数据: {updated_device}")
                            logger.info(f"更新后的字段数: {len(updated_device)}")
                        else:
                            logger.warning(f"无法获取更新后的设备{device_id}数据")
                    except Exception as e:
                        logger.error(f"验证更新结果异常: {str(e)}")
            except Exception as e:
                logger.error(f"MongoDB更新操作异常: {str(e)}", exc_info=True)

                # 记录设备状态历史
                if state_json:
                    try:
                        state = json.loads(state_json)
                        # 异步调用记录状态历史的方法
                        asyncio.create_task(self.history_service.record_status(device_id, state))
                        logger.debug(f"设备{device_id}状态历史记录任务已创建")
                    except Exception as e:
                        logger.warning(f"创建记录设备{device_id}状态历史任务失败: {str(e)}")

        except Exception as e:
            logger.error(f"更新设备状态到数据库异常: {str(e)}", exc_info=True)

    async def _take_snapshot(self) -> None:
        """将Redis中的设备状态保存到MongoDB"""
        try:
            logger.info("开始执行设备状态快照")

            # 检查Redis连接
            try:
                ping_result = await self.redis_client.ping()
                logger.info(f"Redis连接状态: {ping_result}")
            except Exception as e:
                logger.error(f"Redis连接测试失败: {str(e)}")
                return

            # 尝试获取Redis中的所有键，看看是否有设备相关的键
            try:
                # 获取与设备相关的键
                device_keys = await self.redis_client.keys("device:*")
                if device_keys:
                    logger.info(f"Redis中找到{len(device_keys)}个设备相关的键: {device_keys[:5]}...")
                else:
                    logger.warning("Redis中没有找到任何设备相关的键")

                # 获取所有集合
                sets = await self.redis_client.keys("*")
                logger.info(f"Redis中的所有键: {sets[:20]}...")
            except Exception as e:
                logger.error(f"获取Redis键列表失败: {str(e)}")

            # 获取所有Core服务ID
            core_ids = []
            try:
                # 尝试从cores:all集合获取Core服务ID
                core_ids_bytes = await self.redis_client.smembers("cores:all")
                if core_ids_bytes:
                    for core_id in core_ids_bytes:
                        if isinstance(core_id, bytes):
                            core_ids.append(core_id.decode('utf-8'))
                        else:
                            core_ids.append(core_id)
                    logger.info(f"从'cores:all'集合获取到{len(core_ids)}个Core服务ID: {core_ids}")
                else:
                    # 如果没有cores:all集合，使用默认Core ID
                    core_ids = ["default"]
                    logger.info("没有找到Core服务ID，使用默认ID: default")
            except Exception as e:
                logger.error(f"获取Core服务ID列表失败: {str(e)}")
                # 使用默认Core ID
                core_ids = ["default"]
                logger.info("获取Core服务ID异常，使用默认ID: default")

            # 处理每个Core服务的设备
            all_device_ids = []
            for core_id in core_ids:
                try:
                    # 尝试从core:{core_id}:devices集合获取设备ID
                    core_device_ids_bytes = await self.redis_client.smembers(f"core:{core_id}:devices")
                    core_device_ids = []
                    if core_device_ids_bytes:
                        for device_id in core_device_ids_bytes:
                            if isinstance(device_id, bytes):
                                core_device_ids.append(device_id.decode('utf-8'))
                            else:
                                core_device_ids.append(device_id)
                        logger.info(f"从'core:{core_id}:devices'集合获取到{len(core_device_ids)}个设备ID")

                        # 将设备ID和Core ID关联起来
                        for device_id in core_device_ids:
                            all_device_ids.append((device_id, core_id))
                except Exception as e:
                    logger.error(f"获取Core服务{core_id}的设备ID列表失败: {str(e)}")

            # 如果没有从Core服务集合中获取到设备ID，尝试从devices:all集合获取
            if not all_device_ids:
                try:
                    # 从devices:all集合获取设备ID
                    device_ids_bytes = await self.redis_client.smembers("devices:all")
                    if device_ids_bytes:
                        device_ids = []
                        for device_id in device_ids_bytes:
                            if isinstance(device_id, bytes):
                                device_ids.append(device_id.decode('utf-8'))
                            else:
                                device_ids.append(device_id)
                        logger.info(f"从'devices:all'集合获取到{len(device_ids)}个设备ID")

                        # 使用默认Core ID
                        for device_id in device_ids:
                            all_device_ids.append((device_id, "default"))
                except Exception as e:
                    logger.error(f"获取设备ID列表失败: {str(e)}")

            # 如果仍然没有找到设备ID，尝试从键名模式中提取
            if not all_device_ids:
                try:
                    logger.info("没有从集合中找到设备ID，尝试从键名模式中提取")
                    # 获取所有设备状态键
                    state_keys = await self.redis_client.keys("device:*:state")
                    if state_keys:
                        logger.info(f"从键名模式'device:*:state'中找到{len(state_keys)}个键")
                        # 从键名中提取设备ID
                        for key in state_keys:
                            # 键名格式为"device:{id}:state"或"device:{core_id}:{id}:state"
                            key_str = key.decode('utf-8')
                            parts = key_str.split(':')
                            if len(parts) == 3 and parts[0] == 'device' and parts[2] == 'state':
                                device_id = parts[1]
                                all_device_ids.append((device_id, "default"))
                            elif len(parts) == 4 and parts[0] == 'device' and parts[3] == 'state':
                                core_id = parts[1]
                                device_id = parts[2]
                                all_device_ids.append((device_id, core_id))
                        logger.info(f"从键名中提取到{len(all_device_ids)}个设备ID")
                except Exception as e:
                    logger.error(f"从键名提取设备ID失败: {str(e)}")

            if not all_device_ids:
                logger.warning("没有找到设备，快照跳过")
                return

            # 记录找到的设备ID
            logger.info(f"找到{len(all_device_ids)}个设备，准备更新到MongoDB")
            if all_device_ids:
                sample = all_device_ids[:10]
                logger.info(f"设备ID示例: {sample}")

            # 批量获取设备状态
            pipeline = self.redis_client.pipeline()
            for device_id, core_id in all_device_ids:
                # 尝试使用新的键名格式（带有Core ID）
                pipeline.get(f"device:{core_id}:{device_id}:state")
                pipeline.get(f"device:{core_id}:{device_id}:last_change")
                # 同时尝试使用旧的键名格式（不带Core ID）
                pipeline.get(f"device:{device_id}:state")
                pipeline.get(f"device:{device_id}:last_change")

            # 执行管道操作
            results = await pipeline.execute()

            # 更新到MongoDB
            updated_count = 0
            for i in range(0, len(results), 4):
                if i // 4 >= len(all_device_ids):
                    break

                device_id, core_id = all_device_ids[i // 4]

                # 尝试从新的键名格式获取数据
                state_json_new = results[i]
                last_change_json_new = results[i + 1] if i + 1 < len(results) else None

                # 尝试从旧的键名格式获取数据
                state_json_old = results[i + 2] if i + 2 < len(results) else None
                last_change_json_old = results[i + 3] if i + 3 < len(results) else None

                # 使用新格式的数据，如果没有则使用旧格式的数据
                state_json = state_json_new if state_json_new else state_json_old
                last_change_json = last_change_json_new if last_change_json_new else last_change_json_old

                # 记录获取到的数据
                if state_json:
                    logger.debug(f"获取到设备{device_id}（Core: {core_id}）的状态数据")
                else:
                    logger.warning(f"设备{device_id}（Core: {core_id}）没有状态数据")

                if last_change_json:
                    logger.debug(f"获取到设备{device_id}（Core: {core_id}）的最后一次变更数据")
                else:
                    logger.debug(f"设备{device_id}（Core: {core_id}）没有最后一次变更数据")

                if state_json:
                    try:
                        state = json.loads(state_json)
                        # 更新设备状态和基本信息
                        device_data = {
                            "status": state.get("status", "unknown"),
                            "updated_at": datetime.fromtimestamp(state.get("last_sync", int(time.time()))),
                            "core_id": core_id  # 添加Core服务标识
                        }

                        # 添加设备名称和类型
                        if "name" in state:
                            device_data["name"] = state.get("name")

                        # 设备类型 - 注意MongoDB中字段名是"type"
                        if "device_type" in state:
                            device_data["type"] = state.get("device_type")

                        # 从状态中获取硬件信息
                        hardware_info = {}
                        if "hardware_info" in state:
                            hardware_info = state.get("hardware_info", {})
                            logger.debug(f"从状态中获取到设备{device_id}（Core: {core_id}）的硬件信息")

                        # 获取分辨率信息
                        width = hardware_info.get('width', 1080)
                        height = hardware_info.get('height', 1920)

                        # 如果hardware_info中没有分辨率，尝试从display_info获取
                        if "display_info" in state:
                            display_info = state.get("display_info", {})
                            if not width and "width" in display_info:
                                width = display_info.get("width", 1080)
                            if not height and "height" in display_info:
                                height = display_info.get("height", 1920)

                        device_data["config"] = {
                            "resolution": f"{width}x{height}",
                            "cpu_cores": hardware_info.get("cpu_cores", 2),
                            "memory": hardware_info.get("memory_size", 2048),
                            "adb_port": hardware_info.get("adb_port", 5555)
                        }

                        # 添加窗口信息
                        if "window_info" in state:
                            window_info = state.get("window_info", {})
                            device_data["window_info"] = window_info

                        # 添加进程信息
                        if "process_info" in state:
                            process_info = state.get("process_info", {})
                            device_data["process_info"] = process_info

                        # 添加CPU和内存使用率
                        if "cpu_usage" in state:
                            device_data["cpu_usage"] = state.get("cpu_usage")

                        if "memory_usage" in state:
                            device_data["memory_usage"] = state.get("memory_usage")

                        if "network_status" in state:
                            device_data["network_status"] = state.get("network_status")

                        # 如果有最后一次变更信息，添加到设备数据中
                        if last_change_json:
                            try:
                                last_change = json.loads(last_change_json)
                                device_data["last_change"] = {
                                    "old_status": last_change.get("old_status", "unknown"),
                                    "new_status": last_change.get("new_status", "unknown"),
                                    "timestamp": last_change.get("timestamp", int(time.time()))
                                }
                                logger.debug(f"添加设备{device_id}（Core: {core_id}）的最后一次变更信息")
                            except json.JSONDecodeError:
                                logger.warning(f"解析设备{device_id}（Core: {core_id}）的最后一次变更JSON失败")
                            except Exception as e:
                                logger.warning(f"处理设备{device_id}（Core: {core_id}）的最后一次变更异常: {str(e)}")

                        # 确保创建时间字段存在
                        try:
                            # 使用同步方式查询MongoDB
                            existing_device = self.mongo_db.devices.find_one({"_id": device_id})
                            if not existing_device:
                                device_data["created_at"] = datetime.now()
                            elif "created_at" not in existing_device:
                                # 如果设备存在但没有创建时间，添加一个
                                device_data["created_at"] = datetime.now()

                            # 记录快照前的字段数量
                            before_fields = 0
                            if existing_device:
                                before_fields = len(existing_device)
                        except Exception as e:
                            logger.error(f"快照：查询设备{device_id}异常: {str(e)}")
                            # 如果查询失败，默认添加创建时间
                            device_data["created_at"] = datetime.now()
                            before_fields = 0

                        # 记录更新数据的详细内容
                        logger.info(f"快照：准备更新设备{device_id}的数据，字段数: {len(device_data)}")
                        logger.info(f"快照：更新数据内容: {device_data}")

                        # 更新到MongoDB
                        try:
                            result = self.mongo_db.devices.update_one(
                                {"_id": device_id},
                                {"$set": device_data},
                                upsert=True  # 如果设备不存在则创建
                            )

                            logger.info(f"快照：MongoDB更新结果: matched={result.matched_count}, modified={result.modified_count}, upserted={result.upserted_id}")

                            if result.modified_count > 0 or result.upserted_id:
                                logger.info(f"快照：设备信息已更新: {device_id}, 字段数: {len(device_data)}, 之前字段数: {before_fields}")

                                # 验证更新是否成功
                                try:
                                    updated_device = self.mongo_db.devices.find_one({"_id": device_id})
                                    if updated_device:
                                        logger.info(f"快照：更新后的设备数据: {updated_device}")
                                        logger.info(f"快照：更新后的字段数: {len(updated_device)}")
                                    else:
                                        logger.warning(f"快照：无法获取更新后的设备{device_id}数据")
                                except Exception as e:
                                    logger.error(f"快照：验证更新结果异常: {str(e)}")

                                # 记录设备状态历史
                                # 异步调用记录状态历史的方法
                                asyncio.create_task(self.history_service.record_status(device_id, state))
                                logger.debug(f"设备{device_id}状态历史记录任务已创建")
                        except Exception as e:
                            logger.error(f"快照：MongoDB更新操作异常: {str(e)}", exc_info=True)

                        updated_count += 1
                    except json.JSONDecodeError as e:
                        logger.error(f"解析设备{device_id}状态JSON失败: {str(e)}")
                    except Exception as e:
                        logger.error(f"处理设备{device_id}状态异常: {str(e)}", exc_info=True)
                else:
                    logger.warning(f"设备{device_id}在Redis中没有状态数据")

            logger.info(f"设备状态快照完成，共{updated_count}个设备")

        except Exception as e:
            logger.error(f"执行设备状态快照异常: {str(e)}", exc_info=True)


