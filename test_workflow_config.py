#!/usr/bin/env python3
"""
测试工作流配置获取功能
验证Backend是否能正确从Core服务获取真实的工作流配置
"""

import asyncio
import aiohttp
import json
import logging
from motor.motor_asyncio import AsyncIOMotorClient

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_test_task():
    """创建测试任务"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient("mongodb://***************:27017")
        db = client["social_media_automation"]
        
        # 创建测试任务
        test_task = {
            "task_id": "test_workflow_config_123",
            "platform_id": "youtube",
            "workflow_name": "YouTube短视频上传",
            "status": "pending",
            "metadata": {
                "contentType": "shorts",
                "title": "测试短视频",
                "description": "这是一个测试短视频"
            },
            "created_at": "2024-01-01T10:00:00Z"
        }
        
        # 删除可能存在的旧测试任务
        await db.social_tasks.delete_many({"task_id": "test_workflow_config_123"})
        
        # 插入新的测试任务
        result = await db.social_tasks.insert_one(test_task)
        logger.info(f"✅ 创建测试任务成功: {result.inserted_id}")
        
        await client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建测试任务失败: {str(e)}")
        return False

async def test_workflow_config_api():
    """测试工作流配置API"""
    try:
        task_id = "test_workflow_config_123"
        
        async with aiohttp.ClientSession() as session:
            url = f"http://localhost:8000/api/v1/workflow/{task_id}/workflow"
            logger.info(f"请求URL: {url}")
            
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ API调用成功")
                    logger.info(f"✅ 数据源: {data.get('source', 'unknown')}")
                    
                    if data.get('success') and data.get('data'):
                        workflow_data = data['data']
                        logger.info(f"✅ 工作流名称: {workflow_data.get('workflow_name')}")
                        logger.info(f"✅ 工作流版本: {workflow_data.get('workflow_version')}")
                        logger.info(f"✅ 总步骤数: {workflow_data.get('total_steps')}")
                        
                        steps = workflow_data.get('steps', [])
                        logger.info(f"✅ 步骤详情:")
                        for i, step in enumerate(steps):
                            step_id = step.get('id', 'unknown')
                            name = step.get('name', 'Unknown')
                            action = step.get('action', 'unknown')
                            required = step.get('required', False)
                            logger.info(f"   步骤 {i+1}: {name} (ID: {step_id}, Action: {action}, Required: {required})")
                        
                        # 验证是否获取到了真实配置
                        source = data.get('source', '')
                        if 'core' in source or 'config' in source:
                            logger.info(f"✅ 成功获取到真实的工作流配置！数据源: {source}")
                            
                            # 检查是否有真实的步骤配置
                            if len(steps) > 3 and any(step.get('action') for step in steps):
                                logger.info(f"✅ 工作流步骤包含真实的action配置，不是模拟数据")
                                return True
                            else:
                                logger.warning(f"⚠️ 工作流步骤可能仍然是模拟数据")
                                return False
                        else:
                            logger.warning(f"⚠️ 获取到的可能不是真实配置，数据源: {source}")
                            return False
                            
                    else:
                        logger.error(f"❌ API返回数据格式错误: {data}")
                        return False
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API调用失败: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ API测试失败: {str(e)}")
        return False

async def cleanup_test_task():
    """清理测试任务"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient("mongodb://***************:27017")
        db = client["social_media_automation"]
        
        # 删除测试任务
        result = await db.social_tasks.delete_many({"task_id": "test_workflow_config_123"})
        logger.info(f"✅ 清理测试任务成功，删除了 {result.deleted_count} 个任务")
        
        await client.close()
        
    except Exception as e:
        logger.warning(f"⚠️ 清理测试任务失败: {str(e)}")

async def main():
    """主测试函数"""
    logger.info("开始测试工作流配置获取功能")
    
    # 1. 创建测试任务
    logger.info("1. 创建测试任务")
    if not await create_test_task():
        logger.error("创建测试任务失败，终止测试")
        return
    
    # 等待一下确保任务已保存
    await asyncio.sleep(1)
    
    # 2. 测试API
    logger.info("2. 测试工作流配置API")
    success = await test_workflow_config_api()
    
    # 3. 清理测试数据
    logger.info("3. 清理测试数据")
    await cleanup_test_task()
    
    # 4. 输出结果
    if success:
        logger.info("🎉 测试成功！Backend能够正确获取Core服务的真实工作流配置")
    else:
        logger.error("❌ 测试失败！Backend仍然在使用模拟数据或无法获取真实配置")

if __name__ == "__main__":
    asyncio.run(main())
